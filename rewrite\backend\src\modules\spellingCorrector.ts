import fs from "fs";
import nspell from "nspell";

interface INspell {
    correct(word: string): boolean;
    suggest(word: string): string[];
}

class SpellingCorrector {
    private spell: INspell | null = null;

    constructor() {
        this.spell = null;
    }

    loadDictionary(affPath: string = "./dictionaries/en_US.aff", dicPath: string = "./dictionaries/en_US.dic"): void {
        try {
            const aff: string = fs.readFileSync(affPath, "utf-8");
            const dic: string = fs.readFileSync(dicPath, "utf-8");
            this.spell = nspell(aff, dic);
            console.log("SpellingCorrector dictionary loaded successfully");
        } catch (error) {
            console.error("Failed to load SpellingCorrector dictionary:", error);
            throw error;
        }
    }

    correct(word: string): string {
        if (!this.spell) {
            console.warn("Dictionary not loaded, returning original word");
            return word;
        }

        try {
            // Check if word is spelled correctly
            if (this.spell.correct(word)) {
                return word;
            }

            // Get suggestions and return the first one, or original word if no suggestions
            const suggestions: string[] = this.spell.suggest(word);
            return suggestions && suggestions.length > 0 ? suggestions[0] : word;
        } catch (error) {
            console.error("Error in spelling correction:", error);
            return word;
        }
    }
}

export default SpellingCorrector;
