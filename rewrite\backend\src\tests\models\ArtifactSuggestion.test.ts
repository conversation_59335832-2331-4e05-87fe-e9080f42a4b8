import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ArtifactSuggestion Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactSuggestion')];

        const ArtifactSuggestionModule = await import('../../models/ArtifactSuggestion');
        const ArtifactSuggestion = ArtifactSuggestionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ArtifactSuggestion', expect.any(Object), 'artifact_suggestions');
        expect(ArtifactSuggestion).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.search).toBeDefined();
        expect(schemaArg.paths.search.type).toBe(String);
        expect(schemaArg.paths.search.required).toBe(true);

        expect(schemaArg.paths.click).toBeDefined();
        expect(schemaArg.paths.click.type).toBe(Number);
        expect(schemaArg.paths.click.default).toBe(0);

        expect(schemaArg.paths.impressions).toBeDefined();
        expect(schemaArg.paths.impressions.type).toBe(Number);
        expect(schemaArg.paths.impressions.default).toBe(0);

        expect(schemaArg.index).toHaveBeenCalledWith({ search: 1 }, { unique: true });
    });
});
