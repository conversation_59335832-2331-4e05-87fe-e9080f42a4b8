const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const awsKinesis = require('../../modules/awsKinesis');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Kinesis API', () => {

    describe('GET /api/kinesis/listStreams', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/kinesis/listStreams');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if region query parameter is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch streams successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    jest.spyOn(awsKinesis, 'listStreams').mockResolvedValue(streamsList);

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['DeviceName', 'StreamName', 'StreamARN', 'MediaType', 'KmsKeyId', 'Version', 'Status', 'CreationTime', 'DataRetentionInHours', 'Tags', 'IsLive'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    jest.spyOn(awsKinesis, 'listStreams').mockRejectedValue(new Error('Something went wrong'));

                    const res = await request(app)
                        .get('/api/kinesis/listStreams?region=us-east-1')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('GET /api/kinesis/hlsStreamingSessionURL', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/kinesis/hlsStreamingSessionURL');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if streamName is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if minutes is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if region is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if streamMode is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=INVALID&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch HLS streaming URL successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const mockUrl = 'https://example.com/hls/stream.m3u8';
                    jest.spyOn(awsKinesis, 'getHlsStreamingSessionURL').mockResolvedValue(mockUrl);

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('url');
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    jest.spyOn(awsKinesis, 'getHlsStreamingSessionURL').mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .get('/api/kinesis/hlsStreamingSessionURL?streamName=testStream&region=us-east-1&streamMode=LIVE&minutes=60')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
