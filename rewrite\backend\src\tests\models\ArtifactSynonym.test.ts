import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ArtifactSynonym Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactSynonym')];

        const ArtifactSynonymModule = await import('../../models/ArtifactSynonym');
        const ArtifactSynonym = ArtifactSynonymModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ArtifactSynonym', expect.any(Object), 'artifact_synonyms');
        expect(ArtifactSynonym).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.type).toBeDefined();
        expect(schemaArg.paths.type.type).toBe(String);
        expect(schemaArg.paths.type.required).toBe(true);

        expect(schemaArg.paths.word).toBeDefined();
        expect(schemaArg.paths.word.type).toBe(String);
        expect(schemaArg.paths.word.required).toBe(true);

        expect(schemaArg.paths.synonyms).toBeDefined();
        expect(schemaArg.paths.synonyms.type).toEqual([String]);
        expect(schemaArg.paths.synonyms.required).toBe(true);
    });
});
