import { authorizedUser } from '../data/Auth';
import User from '../../models/User';
import { getUser } from '../../queries/User';
import { describe, test, jest, beforeEach, expect } from "@jest/globals";

// Mock external modules
jest.mock('mongoose');
jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            aggregate: jest.fn(),
            find: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('User queries', () => {
    const validUserId = '66e2e452bca74bbdb726369c';
    let aggregateSpy: any;

    beforeEach(() => {
        jest.clearAllMocks(); // Clear mocks before each test
        // Create a spy on User.aggregate
        aggregateSpy = jest.spyOn(User, 'aggregate');
    });

    afterEach(() => {
        aggregateSpy.mockRestore();
    });

    describe('getUser', () => {
        test('should resolve with user details when valid user_id is provided', async () => {
            aggregateSpy.mockResolvedValue([authorizedUser]); // Mock Mongoose aggregate success

            const result = await getUser({ user_id: validUserId });

            expect(result).toBeInstanceOf(Object);
            ['_id', 'name', 'username', 'role_id', 'deletable', 'creation_timestamp', 'is_deleted', 'role', 'permissions'].forEach(prop => {
                expect(result).toHaveProperty(prop);
            });
        });

        test('should reject with error when user_id is not provided', async () => {
            await expect(getUser({} as any)).rejects.toEqual({ message: 'getUser failed. No id provided' });
        });

        test('should reject with an error when aggregate fails', async () => {
            const error = new Error('Database error');
            aggregateSpy.mockRejectedValue(error); // Mock Mongoose aggregate failure

            await expect(getUser({ user_id: validUserId })).rejects.toBe(error);
            expect(aggregateSpy).toHaveBeenCalledTimes(1); // Ensure that the aggregate was called
        });
    });
});