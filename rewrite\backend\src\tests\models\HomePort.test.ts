import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('HomePort Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/HomePort')];

        const HomePortModule = await import('../../models/HomePort');
        const HomePort = HomePortModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('HomePort', expect.any(Object), 'home_ports');
        expect(HomePort).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.lat).toBeDefined();
        expect(schemaArg.paths.lat.type).toBe(Number);
        expect(schemaArg.paths.lat.required).toBe(true);

        expect(schemaArg.paths.lng).toBeDefined();
        expect(schemaArg.paths.lng.type).toBe(Number);
        expect(schemaArg.paths.lng.required).toBe(true);
    });
});
