import { Button, Grid, Modal, FormControl, Checkbox, ListItemText, Autocomplete, TextField, alpha, Typography, Chip } from "@mui/material";
import React, { useState, useEffect } from "react";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../../../ReactDatePicker.css";
import theme from "../../../theme";
import { useApp } from "../../../hooks/AppHook";
import { logEvent } from "../../../utils";
import { getColorList } from "../../../colors";
import MultiSelect from "../../../components/MultiSelect";
import { useMemo } from "react";

const FilterEventModal = ({
    showFilterModal,
    setShowFilterModal,
    events,
    setFilteredEvents,
    filterItems,
    setFilters,
    vessels,
    filters,
    selectedTime,
    setSelectedTime,
    selectedType,
    setSelectedType,
    selectedArea,
    setSelectedArea,
    selectedVessel,
    setSelectedVessel,
    selectedCategory,
    setSelectedCategory,
    timeStart,
    setTimeStart,
    timeEnd,
    setTimeEnd,
    selectedSize,
    setSelectedSize,
    selectedColor,
    setSelectedColor,
    selectedWeapon,
    setSelectedWeapon,
    selectedHostVessel,
    setSelectedHostVessel,
    regionGroups,
}) => {
    const { devMode } = useApp();
    // const [selectedTime, setSelectedTime] = useState("all");
    // const [selectedType, setSelectedType] = useState("both");
    // const [selectedArea, setSelectedArea] = useState([]);
    // const [selectedVessel, setSelectedVessel] = useState([]);
    // const [selectedCategory, setSelectedCategory] = useState([]);
    // const [selectedSize, setSelectedSize] = useState([]);
    // const [selectedColor, setSelectedColor] = useState([]);
    // const [selectedWeapon, setSelectedWeapon] = useState([]);
    // const [timeStart, setTimeStart] = useState(new Date());
    // const [timeEnd, setTimeEnd] = useState(new Date());

    useEffect(() => {
        if (showFilterModal) {
            setSelectedTime(filters?.start_time || filters?.end_time ? "custom" : "all");
            setSelectedType(filters?.type || "both");
            setSelectedArea(filters?.country_flags || []);
            // Handle vessel selection with "Select All" logic
            if (filters?.vessel_ids && filters.vessel_ids.length > 0) {
                const selectedVessels = vessels.filter((v) => filters.vessel_ids.includes(v.vessel_id));
                const allActiveVessels = vessels.filter((v) => v.is_active !== false);

                // Check if all active vessels are selected (equivalent to "Select All")
                const allVesselsSelected = allActiveVessels.every((vessel) => filters.vessel_ids.includes(vessel.vessel_id));

                if (allVesselsSelected && allActiveVessels.length > 0) {
                    // Add "Select All" option to the selection
                    const allOption = {
                        vessel_id: "all",
                        name: "Select All",
                        region_group_object: { name: "Select All" },
                    };
                    setSelectedVessel([allOption, ...selectedVessels]);
                } else {
                    setSelectedVessel(selectedVessels);
                }
            } else {
                setSelectedVessel([]);
            }
            setSelectedCategory(filters?.categories || []);
            setSelectedSize(filters?.sizes || []);
            setSelectedColor(filters?.colors || []);
            setSelectedWeapon(filters?.weapons || []);
            setSelectedHostVessel(
                filters?.host_vessel === true ? "host" : filters?.host_vessel === false ? "non_host" : "both", // If no host_vessel filter is present, it means "both" was selected
            );
            setTimeStart(filters?.start_time ? new Date(filters.start_time) : new Date());
            setTimeEnd(filters?.end_time ? new Date(filters.end_time) : new Date());
        }
    }, [showFilterModal, filters, vessels]);

    const [changed, setChanged] = useState({
        changedTime: false,
        oldTime: "",
        changedType: false,
        oldType: "",
        changedArea: false,
        oldArea: [],
        changedVessel: false,
        oldVessel: [],
        changedCategory: false,
        oldCategory: [],
        changedHostVessel: false,
        oldHostVessel: "",
    });

    const handleClose = (event, reason) => {
        if (reason === "backdropClick") {
            return;
        }
        setShowFilterModal(false);
        if (changed.changedTime) {
            setSelectedTime(changed.oldTime);
        }
        if (changed.changedType) {
            setSelectedType(changed.oldType);
        }
        if (changed.changedArea) {
            setSelectedArea(changed.oldArea);
        }
        if (changed.changedVessel) {
            setSelectedVessel(changed.oldVessel);
        }
        if (changed.changedCategory) {
            setSelectedCategory(changed.oldCategory);
        }
        if (changed.changedHostVessel) {
            setSelectedHostVessel(changed.oldHostVessel);
        }

        setChanged({
            changedTime: false,
            oldTime: "",
            changedType: false,
            oldType: "",
            changedArea: false,
            oldArea: [],
            changedVessel: false,
            oldVessel: [],
            changedCategory: false,
            oldCategory: [],
            changedHostVessel: false,
            oldHostVessel: "",
        });
    };

    const handleSubmit = () => {
        const filters = {};
        const now = dayjs().valueOf();
        let startTimestamp;
        switch (selectedTime) {
            case "last_1_hour":
                startTimestamp = now - 1 * 60 * 60 * 1000;
                break;
            case "last_24_hours":
                startTimestamp = now - 24 * 60 * 60 * 1000;
                break;
            case "last_7_days":
                startTimestamp = now - 7 * 24 * 60 * 60 * 1000;
                break;
            case "last_1_month":
                startTimestamp = now - 30 * 24 * 60 * 60 * 1000;
                break;
            case "all":
                startTimestamp = null;
                break;
            case "custom":
                startTimestamp = dayjs(timeStart).valueOf();
                break;
        }
        if (selectedType) {
            filters.type = selectedType;
        }
        if (selectedArea && selectedArea.length > 0) {
            filters.country_flags = selectedArea;
        }
        if (selectedVessel && selectedVessel.length > 0) {
            // Check if "All" is selected
            const hasAllSelected = selectedVessel.some((v) => v.vessel_id === "all");
            if (hasAllSelected) {
                // If "All" is selected, include all vessel IDs
                filters.vessel_ids = vessels.filter((v) => v.is_active !== false).map((v) => v.vessel_id);
            } else {
                filters.vessel_ids = selectedVessel.map((v) => v.vessel_id);
            }
        }
        if (selectedCategory && selectedCategory.length > 0) {
            filters.categories = selectedCategory;
        }
        if (selectedSize && selectedSize.length > 0) {
            filters.sizes = selectedSize;
        }
        if (selectedColor && selectedColor.length > 0) {
            filters.colors = selectedColor;
        }
        if (selectedWeapon && selectedWeapon.length > 0) {
            filters.weapons = selectedWeapon;
        }
        if (selectedHostVessel === "host") {
            filters.host_vessel = true;
        } else if (selectedHostVessel === "non_host") {
            filters.host_vessel = false;
        }
        // If "both" is selected, don't send host_vessel filter at all
        if (startTimestamp) {
            filters.start_time = dayjs(startTimestamp).valueOf();
            filters.end_time = selectedTime === "custom" ? dayjs(timeEnd).valueOf() : dayjs(now).valueOf();
        }

        setChanged({
            changedTime: false,
            oldTime: "",
            changedType: false,
            oldType: "",
            changedArea: false,
            oldArea: [],
            changedVessel: false,
            oldVessel: [],
            changedCategory: false,
            oldCategory: [],
            changedHostVessel: false,
            oldHostVessel: "",
        });
        setFilters(filters);
        setShowFilterModal(false);
        logEvent("EventFilterApplied", { filters });
    };

    const handleSelectChange = (setSelected, value, label) => {
        setSelected(value);
        setChanged((prev) => {
            const updated = { ...prev };
            if (label === "Time") {
                updated.changedTime = true;
                updated.oldTime = selectedTime;
            }
            if (label === "Type") {
                updated.changedType = true;
                updated.oldType = selectedType;
            }
            if (label === "Category") {
                updated.changedCategory = true;
                updated.oldCategory = selectedCategory;
            }
            if (label === "Vessel") {
                updated.changedVessel = true;
                updated.oldVessel = selectedVessel;
            }
            if (label === "Flag State") {
                updated.changedArea = true;
                updated.oldArea = selectedArea;
            }
            if (label === "Host Vessel") {
                updated.changedHostVessel = true;
                updated.oldHostVessel = selectedHostVessel;
            }
            return updated;
        });
    };

    const handleClear = () => {
        setSelectedTime("all");
        setSelectedType("both");
        setSelectedArea([]);
        setSelectedVessel([]);
        setSelectedCategory([]);
        setSelectedSize([]);
        setSelectedColor([]);
        setSelectedWeapon([]);
        setSelectedHostVessel("non_host");
        setFilteredEvents(events);
        setChanged({
            changedTime: false,
            oldTime: "",
            changedArea: false,
            oldArea: [],
            changedVessel: false,
            oldVessel: [],
            changedCategory: false,
            oldCategory: [],
            changedHostVessel: false,
            oldHostVessel: "",
        });
        setFilters({ host_vessel: false });
    };
    const vesselsByRegionGroup = useMemo(() => {
        const allVessels = vessels
            .filter((v) => v.region_group_id && v.is_active !== false)
            .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group_id) }))
            .sort((a, b) => {
                const groupA = a.region_group_object?.name?.toLowerCase() || "";
                const groupB = b.region_group_object?.name?.toLowerCase() || "";
                // Sort by group name first
                if (groupA < groupB) return -1;
                if (groupA > groupB) return 1;
                // Then sort within the group based on region_group_object.vessel_ids order
                const nameA = a.name?.toLowerCase() || a.vessel_id;
                const nameB = b.name?.toLowerCase() || b.vessel_id;
                return nameA.localeCompare(nameB);
            });

        // Add "Select All" option at the beginning
        const allOption = {
            vessel_id: "all",
            name: "Select All",
            region_group_object: { name: "Select All" },
        };

        return [allOption, ...allVessels];
    }, [vessels, regionGroups]);

    const renderAutocomplete = (items, selected, setSelected, label, multiple = true, getOptionLabel) => (
        <FormControl sx={{ width: "100%", maxHeight: "150px", overflowY: "auto" }} size="small">
            <Autocomplete
                multiple={multiple}
                value={selected}
                onChange={(event, newValue) => handleSelectChange(setSelected, newValue, label)}
                options={items}
                disableCloseOnSelect
                getOptionLabel={(option) =>
                    getOptionLabel ? getOptionLabel(option) : option.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())
                }
                renderInput={(params) => <TextField {...params} label={"Select " + label} variant="outlined" />}
                sx={{ "& .MuiFormLabel-root": { color: alpha("#FFFFFF", 0.6), fontWeight: 400, maxHeight: "250px", overflowY: "auto" } }}
                renderOption={(props, option, { selected }) => {
                    const { key, ...restProps } = props;
                    return (
                        <React.Fragment key={key}>
                            <li {...restProps}>
                                <Checkbox checked={selected} />
                                <ListItemText
                                    primary={
                                        getOptionLabel
                                            ? getOptionLabel(option)
                                            : option.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())
                                    }
                                />
                            </li>

                            {option === "custom" && selected && (
                                <Grid container sx={{ display: "flex", alignItems: "center", justifyContent: "space-around" }}>
                                    <Grid
                                        container
                                        sx={{ display: "flex", alignItems: "center", justifyContent: "space-around" }}
                                        size={{
                                            xs: 12,
                                            md: 5,
                                        }}
                                    >
                                        <Grid
                                            size={{
                                                xs: 12,
                                                md: 12,
                                            }}
                                        >
                                            <Typography>From</Typography>
                                        </Grid>
                                        <Grid
                                            size={{
                                                xs: 12,
                                                md: 12,
                                            }}
                                        >
                                            <DatePicker
                                                showIcon
                                                icon={
                                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z"
                                                            fill="white"
                                                        />
                                                    </svg>
                                                }
                                                maxDate={dayjs().valueOf()}
                                                selected={timeStart}
                                                onChange={(e) => {
                                                    setTimeStart(e);
                                                }}
                                            />
                                        </Grid>
                                    </Grid>
                                    <Grid
                                        container
                                        sx={{ display: "flex", alignItems: "center", justifyContent: "space-around" }}
                                        size={{
                                            xs: 12,
                                            md: 5,
                                        }}
                                    >
                                        <Grid
                                            size={{
                                                xs: 12,
                                                md: 12,
                                            }}
                                        >
                                            <Typography>To</Typography>
                                        </Grid>

                                        <Grid
                                            size={{
                                                xs: 12,
                                                md: 12,
                                            }}
                                        >
                                            <DatePicker
                                                showIcon
                                                icon={
                                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z"
                                                            fill="white"
                                                        />
                                                    </svg>
                                                }
                                                maxDate={dayjs().valueOf()}
                                                selected={timeEnd}
                                                onChange={(e) => {
                                                    setTimeEnd(e);
                                                }}
                                            />
                                        </Grid>
                                    </Grid>
                                </Grid>
                            )}
                        </React.Fragment>
                    );
                }}
                isOptionEqualToValue={(option, value) => option === value}
            />
        </FormControl>
    );

    if (!vessels || !filterItems || Object.keys(filterItems).length === 0) return null;

    const typeMenuItems = ["image", "video", "both"];
    const timeMenuItems = ["last_1_hour", "last_24_hours", "last_7_days", "last_1_month", "all", "custom"];
    const areaMenuItems = filterItems["countryFlags"].filter((v) => v.name).map((v) => v.name);
    const vesselMenuItems = vessels.filter((v) => v).filter((v) => (devMode ? true : v.is_active)); //filterItems['unitIds'].map(v => vessels.find(vessel => vessel.unit_id === v)?.name || v).filter(v => v);
    const categoryMenuItems = (filterItems["superCategories"] || []).filter((v) => v);
    const sizeMenuItems = filterItems["sizes"] || [];
    const colorMenuItems = getColorList("name");
    const weaponMenuItems = filterItems["weapons"] || [];
    const hostVesselMenuItems = ["non_host", "host", "both"];

    return (
        <Modal open={Boolean(showFilterModal)} onClose={handleClose}>
            <ModalContainer title="Filter" onClose={handleClose} showDivider>
                <Grid container direction="row" gap={2} width={{ xs: 300, sm: 500 }} sx={{ maxHeight: "70vh", overflowY: "auto" }}>
                    {renderAutocomplete(timeMenuItems, selectedTime, setSelectedTime, "Time", false)}
                    {renderAutocomplete(typeMenuItems, selectedType, setSelectedType, "Type", false)}
                    <MultiSelect
                        loading={vesselMenuItems.length === 0}
                        options={vesselsByRegionGroup}
                        value={selectedVessel}
                        multiple
                        disableCloseOnSelect
                        groupBy={(o) => (o.vessel_id === "all" ? "Select All" : o.region_group_object?.name || "Other")}
                        showOnlySingleAllSelectOption={true}
                        label="Select Vessels"
                        getOptionLabel={(o) => o.name}
                        isOptionEqualToValue={(o, v) => o.vessel_id === v.vessel_id}
                        renderTags={(value, getTagProps) =>
                            value.map((option, index) => <Chip key={option.vessel_id} label={option.name} size="small" {...getTagProps({ index })} />)
                        }
                        onChange={(e, v) => {
                            // Handle "Select All" option logic
                            const allOption = vesselsByRegionGroup.find((option) => option.vessel_id === "all");
                            const hasAllSelected = v.some((selected) => selected.vessel_id === "all");
                            const wasAllSelected = selectedVessel.some((selected) => selected.vessel_id === "all");

                            // Get all active vessels (excluding the "Select All" option)
                            const allActiveVessels = vesselsByRegionGroup.filter((option) => option.vessel_id !== "all");

                            // Check if all active vessels are now selected
                            const allVesselsNowSelected = allActiveVessels.every((vessel) =>
                                v.some((selected) => selected.vessel_id === vessel.vessel_id),
                            );

                            if (hasAllSelected && !wasAllSelected) {
                                // If "Select All" is newly selected, select all vessels
                                handleSelectChange(setSelectedVessel, [allOption, ...allActiveVessels], "Vessel");
                            } else if (!hasAllSelected && wasAllSelected) {
                                // If "Select All" is unchecked, clear all selections
                                handleSelectChange(setSelectedVessel, [], "Vessel");
                            } else if (hasAllSelected && wasAllSelected) {
                                // If "Select All" was selected and user tries to remove individual vessels,
                                // remove both the individual vessel and "Select All"
                                const filteredV = v.filter((selected) => selected.vessel_id !== "all");
                                handleSelectChange(setSelectedVessel, filteredV, "Vessel");
                            } else if (!hasAllSelected && allVesselsNowSelected && allActiveVessels.length > 0) {
                                // If all vessels are selected (but "Select All" is not in selection), add "Select All"
                                handleSelectChange(setSelectedVessel, [allOption, ...v], "Vessel");
                            } else if (!hasAllSelected) {
                                // If individual vessels are selected/deselected, remove "Select All" from selection
                                const filteredV = v.filter((selected) => selected.vessel_id !== "all");
                                handleSelectChange(setSelectedVessel, filteredV, "Vessel");
                            }
                        }}
                        backgroundColor="transparent"
                        sx={{
                            width: "100%",
                        }}
                    />
                    {renderAutocomplete(areaMenuItems, selectedArea, setSelectedArea, "Flag State")}
                    {renderAutocomplete(categoryMenuItems, selectedCategory, setSelectedCategory, "Category")}
                    {renderAutocomplete(sizeMenuItems, selectedSize, setSelectedSize, "Size")}
                    {renderAutocomplete(colorMenuItems, selectedColor, setSelectedColor, "Color")}
                    {renderAutocomplete(weaponMenuItems, selectedWeapon, setSelectedWeapon, "Weapon")}
                    {renderAutocomplete(hostVesselMenuItems, selectedHostVessel, setSelectedHostVessel, "Host Vessel", false, (option) => {
                        switch (option) {
                            case "non_host":
                                return "Non-host vessels only";
                            case "host":
                                return "Host vessels only";
                            case "both":
                                return "Both";
                            default:
                                return option;
                        }
                    })}
                </Grid>
                <Grid container gap={2} justifyContent="space-between" mt={2}>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                            }}
                            onClick={handleClear}
                        >
                            Clear filters
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": { backgroundColor: theme.palette.custom.mainBlue },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Apply
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FilterEventModal;
