import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockIoEmitter = {
    emit: jest.fn()
};

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    if (definition && typeof definition === 'object') {
        Object.keys(definition).forEach(key => {
            this.paths[key] = definition[key];
        });
    }

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('SessionLog Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/SessionLog')];

        const SessionLogModule = await import('../../models/SessionLog');
        const SessionLog = SessionLogModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('SessionLog', expect.any(Object), 'logs_sessions');
        expect(SessionLog).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const connectTimestamp = schemaArg.paths.connect_timestamp.default();
        expect(typeof connectTimestamp).toBe('string');
        expect(connectTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const environment = schemaArg.paths.environment.default();
        expect(typeof environment).toBe('string');

        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockLog = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', socket_id: 'socket-123' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockLog);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockLog);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});