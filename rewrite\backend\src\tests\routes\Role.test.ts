const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const { rolesList } = require('../data/Roles');
const Permission = require('../../models/Permission');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            bulkWrite: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Roles API', () => {

    describe('GET /api/roles', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/roles');
                    expect(res.status).toBe(401);
                });

                it('should return the roles list', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.find.mockResolvedValue(rolesList);
                    const res = await request(app).get('/api/roles').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'role_id', 'role_name', 'denied_permissions', 'editable', 'deletable', 'creation_timestamp'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.find.mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app).get('/api/roles').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);

    })

    describe('POST /api/roles', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/roles');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', nonAuthToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if role_name is missing or invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: '' });
                    expect(res.status).toBe(400);
                });

                it('should create a role successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.create.mockResolvedValue({ role_name: 'admin', role_id: -1 });

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.create.mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .post('/api/roles')
                        .set('Authorization', authToken)
                        .send({ role_name: 'admin' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/roles/:id', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/roles/123456');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app)
                        .post('/api/roles/123456')
                        .set('Authorization', nonAuthToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if request body is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: '', denied_permissions: '' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if request body has invalid permissions array', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/roles/invalid_id')
                        .set('Authorization', authToken)
                        .send({ role_name: 'valid-role', denied_permissions: ['1', '2'] });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if role does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue(null);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });
                    expect(res.status).toBe(404);
                });

                it('should return 400 if role is not editable', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: false });

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(400);
                });

                it('should return 400 for invalid permissions in denied_permissions array', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: true, denied_permissions: [] });
                    Permission.find.mockResolvedValue([{ permission_id: 1, assignable: true }]);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [99] });

                    expect(res.status).toBe(400);
                });

                it('should return 403 if trying to modify a forbidden permission (branch 1)', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: true, denied_permissions: [1] });
                    Permission.find.mockResolvedValue([{ permission_id: 1, assignable: false }, { permission_id: 2, assignable: true }]);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [2] });

                    expect(res.status).toBe(403);
                });

                it('should return 403 if trying to modify a forbidden permission (branch 2)', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: true, denied_permissions: [] });
                    Permission.find.mockResolvedValue([{ permission_id: 1, assignable: false }, { permission_id: 2, assignable: true }]);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ denied_permissions: [2, 1] });

                    expect(res.status).toBe(403);
                });

                it('should update role successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: true, role_name: 'original_role', denied_permissions: [], save: jest.fn().mockResolvedValue(true) });
                    Permission.find.mockResolvedValue([{ permission_id: 1, assignable: true }, { permission_id: 2, assignable: true }]);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role', denied_permissions: [1] });

                    expect(res.status).toBe(200);
                });

                it('should update role name successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ editable: true, role_name: 'original_role', denied_permissions: [], save: jest.fn().mockResolvedValue(true) });
                    Permission.find.mockResolvedValue([{ permission_id: 1, assignable: true }, { permission_id: 2, assignable: true }]);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user has equal or higher hierarchy number than the role', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const mockRole = { _id: '507f1f77bcf86cd799439011', editable: true, hierarchy_number: 0, save: jest.fn().mockResolvedValue({}) };

                    Role.findById.mockResolvedValue(mockRole);

                    const res = await request(app)
                        .post('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)
                        .send({ role_name: 'updated_role' });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe("You cannot update this role as it exceeds your hierarchy level");
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('DELETE /api/roles/:id', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/roles/123456');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app)
                        .delete('/api/roles/123456')
                        .set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if id is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .delete('/api/roles/invalid_id')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if role does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue(null);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 400 if role is not deletable', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ deletable: false });

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if role is assigned to a user', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ deletable: true, role_id: 'some_role_id' });
                    User.findOne.mockResolvedValue({ _id: '507f1f77bcf86cd799439012' });

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should delete role successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockResolvedValue({ deletable: true, _id: '507f1f77bcf86cd799439011', role_id: 'some_role_id' });
                    User.findOne.mockResolvedValue(null);
                    Role.findOneAndDelete.mockResolvedValue(true);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findById.mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });

                it('should return 403 if user has equal or higher hierarchy number than the role', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const mockRole = { _id: '507f1f77bcf86cd799439011', hierarchy_number: 0, deletable: true };

                    Role.findById.mockResolvedValue(mockRole);

                    const res = await request(app)
                        .delete('/api/roles/507f1f77bcf86cd799439011')
                        .set('Authorization', authToken)

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("You cannot remove this role as it exceeds your hierarchy level");
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('PATCH /api/roles/permissionUpdate', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/roles/permissionUpdate');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have permission', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', nonAuthToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                });

                it('should return 400 if roles_permissions is missing or empty', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [] });
                    expect(res.status).toBe(400);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(500);
                });

                it('should return 403 if user tries to update a role with equal or higher hierarchy level', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 1, editable: true }]);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update any role as it exceeds your hierarchy level');
                });

                it('should return 403 if no roles are editable', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 3, editable: false }]);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011' }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('No roles are permitted to be edited');
                });

                it('should return 403 if roles contain invalid permission IDs', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 3, editable: true, denied_permissions: [99], _doc: { _id: '507f1f77bcf86cd799439011' } }]);
                    Permission.find.mockResolvedValueOnce([{ permission_id: 1, assignable: true }]);
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011', denied_permissions: [99] }] });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('The role(s) have invalid permission IDs');
                });

                it('should update role permissions successfully if id not matched', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 3, editable: true, denied_permissions: [], _doc: { _id: '507f1f77bcf86cd799439022' } }]);
                    Permission.find.mockResolvedValueOnce([{ permission_id: 1, assignable: true }]);
                    Role.bulkWrite.mockResolvedValueOnce({ modifiedCount: 1 });
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439022', denied_permissions: [1] }] });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Role Permissions Updated');
                });

                it('should update role permissions successfully if id matched', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Role.find.mockResolvedValueOnce([{ _id: '507f1f77bcf86cd799439011', hierarchy_number: 3, editable: true, denied_permissions: [], _doc: { _id: '507f1f77bcf86cd799439011' } }]);
                    Permission.find.mockResolvedValueOnce([{ permission_id: 1, assignable: true }]);
                    Role.bulkWrite.mockResolvedValueOnce({ modifiedCount: 1 });
                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({ roles_permissions: [{ _id: '507f1f77bcf86cd799439011', denied_permissions: [1] }] });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Role Permissions Updated');
                });

                it("should return 400 with a descriptive message if an invalid value is provided for 'denied_permissions'", async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const invalidRoleId = "invalidObjectId";

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({
                            roles_permissions: [{
                                _id: invalidRoleId,
                                denied_permissions: [100]
                            }]
                        });

                    expect(res.status).toBe(400);
                });

                it("should return 403 if roles contains non assignable permission id", async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const validRoleId = "507f1f77bcf86cd799439011";
                    const nonMatchingRoleId = "507f1f77bcf86cd799439022";

                    Role.find.mockResolvedValueOnce([
                        {
                            _id: validRoleId,
                            hierarchy_number: 3,
                            editable: true,
                            denied_permissions: [200, 100],
                            _doc: { _id: validRoleId }
                        },
                        {
                            _id: nonMatchingRoleId,
                            hierarchy_number: 3,
                            editable: true,
                            denied_permissions: [300],
                            _doc: { _id: nonMatchingRoleId }
                        }
                    ]);

                    Permission.find.mockResolvedValueOnce(permissionsList);

                    const res = await request(app)
                        .patch('/api/roles/permissionUpdate')
                        .set('Authorization', authToken)
                        .send({
                            roles_permissions: [
                                { _id: validRoleId, denied_permissions: [100] }
                            ]
                        });

                    expect(res.status).toBe(403);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('PATCH /api/roles/reorder', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/roles/reorder');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if roles array is empty', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({ roles: [] });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Roles must be a non-empty array');
                });

                it('should return 400 if role ID is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: 'invalidObjectId', hierarchy_number: 2 }]
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Invalid value \'invalidObjectId\' provided for field \'roles[0]._id\'');
                });

                it('should return 400 if hierarchy_number is not an integer greater than 0', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: mongoose.Types.ObjectId(), hierarchy_number: 0 }]
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Hierarchy number must be an integer greater than 0');
                });

                it('should return 403 if the user is not authorized to update any roles', async () => {
                    const mockUser = JSON.parse(JSON.stringify(authMockResolve, (key, value) => value));
                    mockUser[0].role.hierarchy_number = 5;
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    Role.find.mockResolvedValue([
                        { _id: mongoose.Types.ObjectId(), hierarchy_number: 2 }
                    ]);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: mongoose.Types.ObjectId(), hierarchy_number: 3 }]
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update any roles');
                });

                it('should return 403 if the user is not authorized to update the specified roles', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.find.mockResolvedValue([
                        { _id: mongoose.Types.ObjectId(), hierarchy_number: 2 }
                    ]);
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: mongoose.Types.ObjectId(), hierarchy_number: 3 }]
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('You are not authorized to update these roles');
                });

                it('should successfully update the hierarchy number of allowed roles', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.find.mockResolvedValue([
                        { _id: '678947f96f36057641892649', hierarchy_number: 2 },
                        { _id: '678947f96f36057641892647', hierarchy_number: 1 },
                        { _id: '678947f96f36057641892648', hierarchy_number: 3 },
                        { _id: '678947f96f36057641892645', hierarchy_number: 5 }
                    ]);
                    Role.bulkWrite.mockResolvedValue({ modifiedCount: 1 });

                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [
                                { _id: '678947f96f36057641892649', hierarchy_number: 3 },
                                { _id: '678947f96f36057641892647', hierarchy_number: 4 },
                                { _id: '678947f96f36057641892648', hierarchy_number: 2 },
                                { _id: '678947f96f36057641892645', hierarchy_number: 5 }
                            ]
                        });

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Hierarchy numbers updated successfully for allowed roles');
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.find.mockRejectedValueOnce(new Error('Something went wrong'));
                    const res = await request(app)
                        .patch('/api/roles/reorder')
                        .set('Authorization', authToken)
                        .send({
                            roles: [{ _id: mongoose.Types.ObjectId(), hierarchy_number: 5 }]
                        });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
