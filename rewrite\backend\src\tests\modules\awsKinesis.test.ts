const { listStreams, getHlsStreamingSessionURL, listStreamsInfo } = require('../../modules/awsKinesis');

const mockKinesisVideo = {
    listStreams: jest.fn(),
    listTagsForStream: jest.fn(),
    getDataEndpoint: jest.fn(),
};
const mockKinesisVideoArchivedMedia = {
    getHLSStreamingSessionURL: jest.fn(),
};

jest.mock('aws-sdk', () => {
    return {
        KinesisVideo: jest.fn(() => mockKinesisVideo),
        KinesisVideoArchivedMedia: jest.fn(() => mockKinesisVideoArchivedMedia),
    };
});


describe('listStreamsInfo', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return a list of streams with tags', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream1' }, { StreamName: 'stream2' }],
                NextToken: null,
            }),
        }));

        mockKinesisVideo.listTagsForStream.mockImplementation(({ StreamName }) => ({
            promise: () => Promise.resolve({ Tags: { key: `tag-${StreamName}` } }),
        }));

        await listStreamsInfo({ region: 'us-east-1' });

        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideo.listTagsForStream).toHaveBeenCalledTimes(2);
    });

    it('should handle empty listStreams response', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [],
                NextToken: null,
            }),
        }));

        const result = await listStreamsInfo({ region: 'us-east-1' });

        expect(result).toEqual([]);
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
    });

    it('should handle error if listStreams fails', async () => {
        const error = new Error('listStreams failed');
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.reject(error),
        }));

        await expect(listStreamsInfo({ region: 'us-east-1' })).rejects.toThrow('listStreams failed');
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
    });

    it('should handle error if listTagsForStream fails', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream1' }],
                NextToken: null,
            }),
        }));

        const error = new Error('listTagsForStream failed');
        mockKinesisVideo.listTagsForStream.mockImplementationOnce(() => ({
            promise: () => Promise.reject(error),
        }));

        await expect(listStreamsInfo({ region: 'us-east-1' })).rejects.toThrow('listTagsForStream failed');
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideo.listTagsForStream).toHaveBeenCalledTimes(1);
    });

    it('should handle pagination correctly', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream1' }],
                NextToken: 'token',
            }),
        }));
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream2' }],
                NextToken: null,
            }),
        }));

        mockKinesisVideo.listTagsForStream.mockImplementation(({ StreamName }) => ({
            promise: () => Promise.resolve({ Tags: { key: `tag-${StreamName}` } }),
        }));

        await listStreamsInfo({ region: 'us-east-1' });

        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(2);
        expect(mockKinesisVideo.listTagsForStream).toHaveBeenCalledTimes(2);
    });
});


describe('listStreams', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return a list of streams with tags and live status', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream1' }, { StreamName: 'stream2' }],
                NextToken: null,
            }),
        }));
        mockKinesisVideo.listTagsForStream.mockImplementation(({ StreamName }) => ({
            promise: () => Promise.resolve({ Tags: { key: `tag-${StreamName}` } }),
        }));
        mockKinesisVideo.getDataEndpoint.mockImplementation(() => ({
            promise: () => Promise.resolve({ DataEndpoint: 'https://data-endpoint-url' }),
        }));
        mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ HLSStreamingSessionURL: 'https://hls-url' }),
        })).mockImplementationOnce(() => ({
            promise: () => Promise.reject(new Error('No fragments found in the streaming session url')),
        }));

        const result = await listStreams({ region: 'us-east-1' });

        expect(result).toEqual([
            {
                StreamName: 'stream1',
                Tags: { key: 'tag-stream1' },
                IsLive: true,
            },
            {
                StreamName: 'stream2',
                Tags: { key: 'tag-stream2' },
                IsLive: false,
            },
        ]);
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideo.listTagsForStream).toHaveBeenCalledTimes(2);
        expect(mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL).toHaveBeenCalledTimes(2);
    });

    it('should throw an error if listStreams fails', async () => {
        const error = new Error('listStreams error');
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.reject(error),
        }));

        await expect(listStreams({ region: 'us-east-1' })).rejects.toThrow('listStreams error');
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
    });

    it('should return an empty list if no streams are found', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ StreamInfoList: [], NextToken: null }),
        }));

        const result = await listStreams({ region: 'us-east-1' });

        expect(result).toEqual([]);
        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(1);
    });

    it('should handle pagination in listStreams', async () => {
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream1' }],
                NextToken: 'token',
            }),
        }));
        mockKinesisVideo.listStreams.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({
                StreamInfoList: [{ StreamName: 'stream2' }],
                NextToken: null,
            }),
        }));
        mockKinesisVideo.listTagsForStream.mockImplementation(({ StreamName }) => ({
            promise: () => Promise.resolve({ Tags: { key: `tag-${StreamName}` } }),
        }));

        await listStreams({ region: 'us-east-1' });

        expect(mockKinesisVideo.listStreams).toHaveBeenCalledTimes(2);
    });
});

describe('getHlsStreamingSessionURL', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return an HLS URL for a live stream', async () => {
        mockKinesisVideo.getDataEndpoint.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ DataEndpoint: 'https://data-endpoint-url' }),
        }));
        mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ HLSStreamingSessionURL: 'https://hls-url' }),
        }));

        const result = await getHlsStreamingSessionURL({ streamName: 'stream1', region: 'us-east-1' });

        expect(result).toBe('https://hls-url');
        expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL).toHaveBeenCalledTimes(1);
    });

    it('should return an HLS URL for an on-demand video', async () => {
        mockKinesisVideo.getDataEndpoint.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ DataEndpoint: 'https://data-endpoint-url' }),
        }));
        mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ HLSStreamingSessionURL: 'https://hls-url' }),
        }));

        const result = await getHlsStreamingSessionURL({ streamName: 'stream1', region: 'us-east-1', streamMode: 'ON_DEMAND' });

        expect(result).toBe('https://hls-url');
        expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL).toHaveBeenCalledTimes(1);
    });

    it('should throw an error if getDataEndpoint fails', async () => {
        const error = new Error('getDataEndpoint error');
        mockKinesisVideo.getDataEndpoint.mockImplementationOnce(() => ({
            promise: () => Promise.reject(error),
        }));

        await expect(getHlsStreamingSessionURL({ streamName: 'stream1', region: 'us-east-1' })).rejects.toThrow('getDataEndpoint error');
        expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL).toHaveBeenCalledTimes(0);
    });

    it('should handle failed HLS URL generation', async () => {
        mockKinesisVideo.getDataEndpoint.mockImplementationOnce(() => ({
            promise: () => Promise.resolve({ DataEndpoint: 'https://data-endpoint-url' }),
        }));
        mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL.mockImplementationOnce(() => ({
            promise: () => Promise.reject(new Error('Failed to generate HLS URL')),
        }));

        await expect(getHlsStreamingSessionURL({ streamName: 'stream1', region: 'us-east-1' })).rejects.toThrow('Failed to generate HLS URL');
        expect(mockKinesisVideo.getDataEndpoint).toHaveBeenCalledTimes(1);
        expect(mockKinesisVideoArchivedMedia.getHLSStreamingSessionURL).toHaveBeenCalledTimes(1);
    });
});
