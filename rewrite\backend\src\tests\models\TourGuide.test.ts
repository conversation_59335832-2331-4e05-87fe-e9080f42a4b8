import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; },
    Boolean: function() { return 'mock-boolean'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; },
        Boolean: function() { return 'mock-boolean'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('TourGuide Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/TourGuide')];

        const TourGuideModule = await import('../../models/TourGuide');
        const TourGuide = TourGuideModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('TourGuide', expect.any(Object));
        expect(TourGuide).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.user_id).toBeDefined();
        expect(schemaArg.paths.user_id.type).toBeDefined();
        expect(schemaArg.paths.user_id.required).toBe(true);

        expect(schemaArg.paths.maps).toBeDefined();
        expect(schemaArg.paths.maps.type).toBeDefined();
        expect(schemaArg.paths.maps.default).toBe(false);

        expect(schemaArg.paths.streams).toBeDefined();
        expect(schemaArg.paths.streams.type).toBeDefined();
        expect(schemaArg.paths.streams.default).toBe(false);

        expect(schemaArg.paths.events).toBeDefined();
        expect(schemaArg.paths.events.type).toBeDefined();
        expect(schemaArg.paths.events.default).toBe(false);

        expect(schemaArg.paths.notifications).toBeDefined();
        expect(schemaArg.paths.notifications.type).toBeDefined();
        expect(schemaArg.paths.notifications.default).toBe(false);
    });
});
