import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('InviteToken Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/InviteToken')];

        const InviteTokenModule = await import('../../models/InviteToken');
        const InviteToken = InviteTokenModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('InviteToken', expect.any(Object), 'invite_tokens');
        expect(InviteToken).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.token).toBeDefined();
        expect(schemaArg.paths.token.type).toBe(String);
        expect(schemaArg.paths.token.required).toBe(true);

        expect(schemaArg.paths.invited_by).toBeDefined();
        expect(schemaArg.paths.invited_by.type).toBeDefined();
        expect(schemaArg.paths.invited_by.required).toBe(true);

        expect(schemaArg.paths.email).toBeDefined();
        expect(schemaArg.paths.email.type).toBe(String);
        expect(schemaArg.paths.email.required).toBe(true);

        expect(schemaArg.paths.organization_id).toBeDefined();
        expect(schemaArg.paths.organization_id.type).toBeDefined();

        expect(schemaArg.paths.role_id).toBeDefined();
        expect(schemaArg.paths.role_id.type).toBe(Number);
        expect(schemaArg.paths.role_id.required).toBe(true);

        expect(schemaArg.paths.role).toBeDefined();
        expect(schemaArg.paths.role.type).toBe(String);
        expect(schemaArg.paths.role.required).toBe(true);

        expect(schemaArg.paths.allowed_vessels).toBeDefined();
        expect(schemaArg.paths.allowed_vessels.type).toBeDefined();
        expect(schemaArg.paths.allowed_vessels.default).toEqual([]);

        expect(schemaArg.paths.short_token).toBeDefined();
        expect(schemaArg.paths.short_token.type).toBe(String);
        expect(schemaArg.paths.short_token.required).toBe(true);
        expect(schemaArg.paths.short_token.unique).toBe(true);

        expect(schemaArg.paths.is_used).toBeDefined();
        expect(schemaArg.paths.is_used.type).toBe(Boolean);
        expect(schemaArg.paths.is_used.default).toBe(false);

        expect(schemaArg.paths.is_deleted).toBeDefined();
        expect(schemaArg.paths.is_deleted.type).toBe(Boolean);
        expect(schemaArg.paths.is_deleted.default).toBe(false);
    });
});
