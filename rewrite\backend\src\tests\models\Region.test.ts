import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('Region Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Region')];

        const RegionModule = await import('../../models/Region');
        const Region = RegionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Region', expect.any(Object));
        expect(Region).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.paths.value).toBeDefined();
        expect(schemaArg.paths.value.type).toBe(String);
        expect(schemaArg.paths.value.required).toBe(true);
        expect(schemaArg.paths.value.unique).toBe(true);

        expect(schemaArg.paths.is_live).toBeDefined();
        expect(schemaArg.paths.is_live.type).toBe(Boolean);
        expect(schemaArg.paths.is_live.required).toBe(true);

        expect(schemaArg.paths.timezone).toBeDefined();
        expect(schemaArg.paths.timezone.type).toBe(String);
        expect(schemaArg.paths.timezone.required).toBe(true);
    });
});
