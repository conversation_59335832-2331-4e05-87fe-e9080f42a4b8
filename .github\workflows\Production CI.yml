name: Production CI

on:
  push:
    branches: [ "main" ]

jobs:
  build:

    runs-on: autoscaled-ec2
    permissions:
      contents: read
      actions: read
      statuses: write
    strategy:
      matrix:
        idx: [1, 2]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'

##############################################################################################
################################ Production CI ###############################################
##############################################################################################

    - name: Echo runner name
      run: |
        echo "This job is running on runner: ${{ runner.name }}"

    - name:
        Deploy to portal.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      run: |
        echo "Deploying to quartermaster-webapp"
        cd ~/app/
        git restore .
        git pull

    - name:
        Deploy to portal.quartermaster.us - Install Backend Dependencies
      run: |
        cd ~/app/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Install Frontend Dependencies
      run: |
        cd ~/app/frontend/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Clean and Create frontend .env File
      run: |
        cd ~/app/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Frontend .env File with Secrets
      run: |
        cd ~/app/frontend/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US_FRONTEND }}" > .env

    - name: 
        Deploy to portal.quartermaster.us - Set BUILD_ID
      id: buildid
      run: echo "BUILD_ID=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT
  
    - name:
        Deploy to portal.quartermaster.us - Build Frontend
      run: |
        cd ~/app/frontend/
        BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build

    - name: 
        Deploy to portal.quartermaster.us - Syslink Switch
      run: |
        cd ~/app/frontend/
        ln -sfn dist-${{ steps.buildid.outputs.BUILD_ID }} dist

    - name: 
        Deploy to portal.quartermaster.us - Cleanup Old Builds
      run: |
        cd ~/app/frontend/
        ls -dt dist-* | tail -n +4 | xargs -r rm -rf
    
    - name:
        Deploy to portal.quartermaster.us - Clean and Create backend .env File
      run: |
        cd ~/app/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Backend .env File with Secrets
      run: |
        cd ~/app/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to portal.quartermaster.us - Restart PM2 Process
      run: |
        pm2 restart 'Quartermaster Webapp'

##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        echo "URL=https://infra.quartermaster.us" >> $GITHUB_ENV

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nProduction CI (Autoscale) - ${{ runner.name }}\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nProduction CI (Autoscale) - ${{ runner.name }}\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}