import { IconButton, Tooltip } from "@mui/material";
import { BorderColor } from "@mui/icons-material";
import theme from "../theme";

export default function EditButton({ onClick, buttonStyle = {}, iconStyle = {}, ...props }) {
    return (
        <Tooltip enterDelay={300} title="Edit" placement="bottom">
            <IconButton
                onClick={onClick}
                sx={{
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    borderRadius: "5px",
                    padding: "5px",
                    color: "#9747FF",
                    bgcolor: "#9747FF0D",
                    ...buttonStyle,
                }}
                {...props}
            >
                <BorderColor fontSize="small" sx={{ ...iconStyle }} />
            </IconButton>
        </Tooltip>
    );
}
