/**
 * Database Module Mocks
 * 
 * Professional mocks for database connections and operations
 */

import { jest } from '@jest/globals';

// ============================================================================
// MONGOOSE CONNECTION MOCKS
// ============================================================================

export const createMongooseConnectionMock = () => ({
    connect: jest.fn().mockResolvedValue({}),
    disconnect: jest.fn().mockResolvedValue({}),
    connection: {
        readyState: 1, // Connected
        on: jest.fn(),
        once: jest.fn(),
        emit: jest.fn(),
        close: jest.fn().mockResolvedValue({}),
        db: {
            databaseName: 'test-database',
            admin: jest.fn().mockReturnValue({
                ping: jest.fn().mockResolvedValue({})
            })
        }
    },
    model: jest.fn().mockReturnValue({}),
    Schema: jest.fn().mockImplementation(() => ({
        pre: jest.fn(),
        post: jest.fn(),
        index: jest.fn(),
        methods: {},
        statics: {},
        virtual: jest.fn().mockReturnValue({
            get: jest.fn(),
            set: jest.fn()
        })
    })),
    Types: {
        ObjectId: jest.fn().mockImplementation((id) => id || '507f1f77bcf86cd799439011')
    }
});

// ============================================================================
// DATABASE CONNECTION SCENARIOS
// ============================================================================

export const setupDatabaseScenarios = () => {
    const mockConnection = createMongooseConnectionMock();
    
    return {
        mockConnection,
        
        // Scenario: Successful connection
        connectionSuccess: () => {
            mockConnection.connect.mockResolvedValue({});
            mockConnection.connection.readyState = 1;
        },
        
        // Scenario: Connection failure
        connectionFailure: (errorMessage = 'Connection failed') => {
            const error = new Error(errorMessage);
            mockConnection.connect.mockRejectedValue(error);
            mockConnection.connection.readyState = 0;
            return error;
        },
        
        // Scenario: Connection timeout
        connectionTimeout: () => {
            const error = new Error('Connection timeout');
            error.name = 'MongoTimeoutError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        
        // Scenario: Authentication failure
        authenticationFailure: () => {
            const error = new Error('Authentication failed');
            error.name = 'MongoAuthError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        
        // Scenario: Network error
        networkError: () => {
            const error = new Error('Network error');
            error.name = 'MongoNetworkError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        
        // Scenario: Disconnection
        disconnection: () => {
            mockConnection.disconnect.mockResolvedValue({});
            mockConnection.connection.readyState = 0;
        }
    };
};

// ============================================================================
// DATABASE OPERATION MOCKS
// ============================================================================

export const createDatabaseOperationMocks = () => ({
    // Transaction mocks
    startSession: jest.fn().mockResolvedValue({
        startTransaction: jest.fn(),
        commitTransaction: jest.fn().mockResolvedValue({}),
        abortTransaction: jest.fn().mockResolvedValue({}),
        endSession: jest.fn().mockResolvedValue({})
    }),
    
    // Collection mocks
    collection: jest.fn().mockReturnValue({
        insertOne: jest.fn().mockResolvedValue({ insertedId: '507f1f77bcf86cd799439011' }),
        insertMany: jest.fn().mockResolvedValue({ insertedIds: ['507f1f77bcf86cd799439011'] }),
        findOne: jest.fn().mockResolvedValue({}),
        find: jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([]),
            limit: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            sort: jest.fn().mockReturnThis()
        }),
        updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
        updateMany: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
        deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
        deleteMany: jest.fn().mockResolvedValue({ deletedCount: 1 }),
        countDocuments: jest.fn().mockResolvedValue(0),
        aggregate: jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([])
        }),
        createIndex: jest.fn().mockResolvedValue('index_name'),
        dropIndex: jest.fn().mockResolvedValue({})
    }),
    
    // Database admin mocks
    admin: jest.fn().mockReturnValue({
        ping: jest.fn().mockResolvedValue({}),
        serverStatus: jest.fn().mockResolvedValue({
            ok: 1,
            version: '4.4.0'
        }),
        listDatabases: jest.fn().mockResolvedValue({
            databases: [{ name: 'test-database', sizeOnDisk: 1024 }]
        })
    })
});

// ============================================================================
// MULTIPLE DATABASE CONNECTION MOCKS
// ============================================================================

export const createMultiDatabaseMocks = () => ({
    // Main database (qm)
    qm: createMongooseConnectionMock(),
    
    // AI database (qmai)
    qmai: createMongooseConnectionMock(),
    
    // Shared database (qmShared)
    qmShared: createMongooseConnectionMock(),
    
    // Setup all connections
    setupAllConnections: function() {
        this.qm.connection.db.databaseName = 'quartermaster';
        this.qmai.connection.db.databaseName = 'quartermaster-ai';
        this.qmShared.connection.db.databaseName = 'quartermaster-shared';
        
        return {
            qm: this.qm,
            qmai: this.qmai,
            qmShared: this.qmShared
        };
    },
    
    // Simulate connection failures
    simulateConnectionFailures: function(databases: string[] = ['qm', 'qmai', 'qmShared']) {
        databases.forEach(db => {
            if (this[db]) {
                this[db].connect.mockRejectedValue(new Error(`${db} connection failed`));
                this[db].connection.readyState = 0;
            }
        });
    },
    
    // Simulate successful connections
    simulateSuccessfulConnections: function(databases: string[] = ['qm', 'qmai', 'qmShared']) {
        databases.forEach(db => {
            if (this[db]) {
                this[db].connect.mockResolvedValue({});
                this[db].connection.readyState = 1;
            }
        });
    }
});

// ============================================================================
// DATABASE TEST UTILITIES
// ============================================================================

export const setupDatabaseTest = () => {
    const mockConnections = createMultiDatabaseMocks();
    const mockOperations = createDatabaseOperationMocks();
    
    return {
        mockConnections,
        mockOperations
    };
};

export const expectDatabaseConnected = (mockConnection: any, connectionString?: string) => {
    expect(mockConnection.connect).toHaveBeenCalled();
    if (connectionString) {
        expect(mockConnection.connect).toHaveBeenCalledWith(connectionString, expect.any(Object));
    }
};

export const expectDatabaseDisconnected = (mockConnection: any) => {
    expect(mockConnection.disconnect).toHaveBeenCalled();
};

export const expectTransactionStarted = (mockSession: any) => {
    expect(mockSession.startTransaction).toHaveBeenCalled();
};

export const expectTransactionCommitted = (mockSession: any) => {
    expect(mockSession.commitTransaction).toHaveBeenCalled();
};

export const expectTransactionAborted = (mockSession: any) => {
    expect(mockSession.abortTransaction).toHaveBeenCalled();
};

// ============================================================================
// DATABASE HEALTH CHECK MOCKS
// ============================================================================

export const createDatabaseHealthMocks = () => ({
    checkConnection: jest.fn().mockResolvedValue({ status: 'connected', latency: 10 }),
    checkAllConnections: jest.fn().mockResolvedValue({
        qm: { status: 'connected', latency: 10 },
        qmai: { status: 'connected', latency: 15 },
        qmShared: { status: 'connected', latency: 12 }
    }),
    getDatabaseStats: jest.fn().mockResolvedValue({
        collections: 10,
        documents: 1000,
        dataSize: 1024000,
        indexSize: 512000
    }),
    getConnectionInfo: jest.fn().mockResolvedValue({
        host: 'localhost',
        port: 27017,
        database: 'test-database',
        readyState: 1
    })
});

// ============================================================================
// CLEANUP
// ============================================================================

export const cleanupDatabaseTest = () => {
    jest.clearAllMocks();
};
