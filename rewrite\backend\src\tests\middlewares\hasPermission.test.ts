const hasPermission = require("../../middlewares/hasPermission");

describe('hasPermission middleware', () => {
    let req, res, next;

    beforeEach(() => {
        req = {};
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
        next = jest.fn();
    });

    it('should call next if user has all required permissions', () => {
        req.user = {
            permissions: [
                { permission_id: 1 },
                { permission_id: 2 }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expect(next).toHaveBeenCalled();
    });

    it('should return 403 if user is missing a required permission', () => {
        req.user = {
            permissions: [
                { permission_id: 1 }
            ]
        };

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.json).toHaveBeenCalledWith({ message: 'You cannot access this resource' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should call next if api_key_id is present', () => {
        req.api_key_id = 'some-api-key';

        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expect(next).toHaveBeenCalled();
    });

    it('should return 403 if neither user nor api_key_id is present', () => {
        const permissions = [1, 2];

        hasPermission(permissions, req, res, next);

        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.json).toHaveBeenCalledWith({ message: 'Login to access this resource' });
        expect(next).not.toHaveBeenCalled();
    });
});
