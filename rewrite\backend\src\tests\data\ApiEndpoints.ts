const apiEndpointsList = [
    {
        "_id": "66f2b3765fc1d8f82af7c728",
        "endpoint_id": 101,
        "name": "FETCH_TOKEN",
        "category": "USERS",
        "is_public": true
    },
    {
        "_id": "66f2b3b25fc1d8f82af7c729",
        "endpoint_id": 102,
        "name": "FETCH_USER",
        "category": "USERS",
        "is_public": true
    },
    {
        "_id": "66f2b3d45fc1d8f82af7c72a",
        "endpoint_id": 103,
        "name": "FETCH_USERS_LIST",
        "category": "USERS",
        "is_public": false
    },
    {
        "_id": "66f2b3e05fc1d8f82af7c72b",
        "endpoint_id": 104,
        "name": "CREATE_USER",
        "category": "USERS",
        "is_public": false
    },
    {
        "_id": "66f2b3fa5fc1d8f82af7c72d",
        "endpoint_id": 105,
        "name": "UPDATE_USER_ROLE",
        "category": "USERS",
        "is_public": false
    },
    {
        "_id": "66f2b4095fc1d8f82af7c72e",
        "endpoint_id": 106,
        "name": "DELETE_USER",
        "category": "USERS",
        "is_public": false
    },
    {
        "_id": "66f2b41e5fc1d8f82af7c72f",
        "endpoint_id": 107,
        "name": "FETCH_PASSWORD_RESET_TOKEN",
        "category": "USERS",
        "is_public": true
    },
    {
        "_id": "66f2b42d5fc1d8f82af7c730",
        "endpoint_id": 108,
        "name": "UPDATE_PASSWORD",
        "category": "USERS",
        "is_public": true
    },
    {
        "_id": "66f2b45d5fc1d8f82af7c731",
        "endpoint_id": 201,
        "name": "FETCH_STREAMS_LIST",
        "category": "STREAMS",
        "is_public": false
    },
    {
        "_id": "66f2b46a5fc1d8f82af7c732",
        "endpoint_id": 202,
        "name": "FETCH_STREAM_URL",
        "category": "STREAMS",
        "is_public": false
    },
    {
        "_id": "66f2e56d5fc1d8f82af7c73d",
        "endpoint_id": 301,
        "name": "FETCH_REGIONS",
        "category": "REGIONS",
        "is_public": false
    },
    {
        "_id": "66f2e5a05fc1d8f82af7c73e",
        "endpoint_id": 401,
        "name": "FETCH_COORDINATES",
        "category": "VESSEL LOCATIONS",
        "is_public": false
    },
    {
        "_id": "66f2e5c15fc1d8f82af7c73f",
        "endpoint_id": 501,
        "name": "FETCH_ARTIFACTS",
        "category": "VESSEL ARTIFACTS",
        "is_public": false
    },
    {
        "_id": "66f2e5da5fc1d8f82af7c740",
        "endpoint_id": 601,
        "name": "FETCH_ROLES",
        "category": "ROLES",
        "is_public": false
    },
    {
        "_id": "66f2e5e55fc1d8f82af7c741",
        "endpoint_id": 602,
        "name": "CREATE_ROLE",
        "category": "ROLES",
        "is_public": false
    },
    {
        "_id": "66f2e5f05fc1d8f82af7c742",
        "endpoint_id": 603,
        "name": "UPDATE_ROLE",
        "category": "ROLES",
        "is_public": false
    },
    {
        "_id": "66f2e5fb5fc1d8f82af7c743",
        "endpoint_id": 604,
        "name": "DELETE_ROLE",
        "category": "ROLES",
        "is_public": false
    },
    {
        "_id": "66f2e63a5fc1d8f82af7c744",
        "endpoint_id": 701,
        "name": "FETCH_PERMISSIONS",
        "category": "PERMISSIONS",
        "is_public": false
    },
    {
        "_id": "66f2e6655fc1d8f82af7c745",
        "endpoint_id": 801,
        "name": "FETCH_SESSION_LOGS",
        "category": "LOGS",
        "is_public": false
    },
    {
        "_id": "66f2e6745fc1d8f82af7c746",
        "endpoint_id": 802,
        "name": "FETCH_SESSION_LOG_BY_ID",
        "category": "LOGS",
        "is_public": false
    },
    {
        "_id": "66f2e6f85fc1d8f82af7c747",
        "endpoint_id": 901,
        "name": "FETCH_FILE_URL",
        "category": "STORAGE",
        "is_public": true
    },
    {
        "_id": "6707ddb2af2c1b345bd06b91",
        "endpoint_id": 1001,
        "name": "FETCH_STATISTICS",
        "category": "STATISTICS",
        "is_public": false
    }
]

module.exports = {
    apiEndpointsList
}