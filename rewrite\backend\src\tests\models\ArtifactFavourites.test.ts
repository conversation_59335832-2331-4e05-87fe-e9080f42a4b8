import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockIoEmitter = {
    emit: jest.fn()
};

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    if (definition && typeof definition === 'object') {
        Object.keys(definition).forEach(key => {
            this.paths[key] = definition[key];
        });
    }

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ArtifactFavourites Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactFavourites')];

        const ArtifactFavouritesModule = await import('../../models/ArtifactFavourites');
        const ArtifactFavourites = ArtifactFavouritesModule.default;

        expect(mockDb.qm.model).toHaveBeenCalled();
        expect(ArtifactFavourites).toBeDefined();

        const artifactFavouritesCall = mockDb.qm.model.mock.calls.find((call: any) => call[0] === 'ArtifactFavourites');
        if (!artifactFavouritesCall) {
            throw new Error('ArtifactFavourites model not found');
        }
        const schemaArg = artifactFavouritesCall[1];

        expect(schemaArg.paths).toBeDefined();

        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockFavourite = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', user_id: 'user-id', artifact_id: 'artifact-id' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(null);
            saveHookFn(mockFavourite);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(null);
            deleteHookFn(mockFavourite);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});
