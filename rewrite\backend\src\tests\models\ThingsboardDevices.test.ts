import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qmShared: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ThingsboardDevices Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ThingsboardDevices')];

        const ThingsboardDevicesModule = await import('../../models/ThingsboardDevices');
        const ThingsboardDevices = ThingsboardDevicesModule.default;

        expect(mockDb.qmShared.model).toHaveBeenCalledWith('ThingsboardDevices', expect.any(Object), 'thingsboard_devices');
        expect(ThingsboardDevices).toBeDefined();

        const schemaArg = mockDb.qmShared.model.mock.calls[0][1];

        expect(schemaArg.paths.deviceId).toBeDefined();
        expect(schemaArg.paths.deviceId.type).toBe(String);
        expect(schemaArg.paths.deviceId.required).toBe(true);
        expect(schemaArg.paths.deviceId.unique).toBe(true);

        expect(schemaArg.paths.dashboardId).toBeDefined();
        expect(schemaArg.paths.dashboardId.type).toBe(String);
        expect(schemaArg.paths.dashboardId.required).toBe(false);

        expect(schemaArg.paths.deviceName).toBeDefined();
        expect(schemaArg.paths.deviceName.type).toBe(String);
        expect(schemaArg.paths.deviceName.required).toBe(true);

        expect(schemaArg.paths.accessToken).toBeDefined();
        expect(schemaArg.paths.accessToken.type).toBe(String);
        expect(schemaArg.paths.accessToken.required).toBe(true);

        expect(schemaArg.index).toHaveBeenCalledWith(
            { dashboardId: 1 },
            {
                unique: true,
                partialFilterExpression: { dashboardId: { $type: "string" } },
            }
        );
    });
});
