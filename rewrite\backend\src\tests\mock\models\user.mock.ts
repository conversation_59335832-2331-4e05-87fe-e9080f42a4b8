import { jest } from '@jest/globals';

export const createMockUser = (overrides: any = {}) => ({
    _id: '507f1f77bcf86cd799439011',
    name: 'Test User',
    email: '<EMAIL>',
    password: 'hashedPassword',
    is_deleted: false,
    is_active: true,
    jwt_tokens: ['mock-jwt-token'],
    role_id: 1,
    organization_id: '507f1f77bcf86cd799439012',
    creation_timestamp: new Date().toISOString(),
    ...overrides
});

export const createMockDeletedUser = (overrides: any = {}) =>
    createMockUser({ is_deleted: true, ...overrides });

export const createMockInactiveUser = (overrides: any = {}) =>
    createMockUser({ is_active: false, ...overrides });

export const createUserModelMock = () => ({
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findOneAndUpdate: jest.fn(),
    findOneAndDelete: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
    deleteOne: jest.fn(),
    deleteMany: jest.fn(),
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
    distinct: jest.fn(),
    save: jest.fn(),
    markModified: jest.fn(),
    toObject: jest.fn(),
    toJSON: jest.fn(),
    schema: {}
});

// ============================================================================
// USER DOCUMENT MOCK
// ============================================================================

export const createMockUserDocument = (data: any = {}) => {
    const userData = createMockUser(data);
    
    return {
        ...userData,
        save: jest.fn().mockResolvedValue(userData),
        remove: jest.fn().mockResolvedValue(userData),
        deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
        markModified: jest.fn(),
        toObject: jest.fn().mockReturnValue(userData),
        toJSON: jest.fn().mockReturnValue(userData),
        isModified: jest.fn().mockReturnValue(false),
        isNew: false
    };
};

// ============================================================================
// USER SCHEMA MOCK
// ============================================================================

export const createUserSchemaMock = () => ({
    pre: jest.fn(),
    post: jest.fn(),
    index: jest.fn(),
    methods: {},
    statics: {},
    paths: {
        name: { type: String, required: true },
        email: { type: String, required: true, unique: true },
        password: { type: String, required: true },
        is_deleted: { type: Boolean, default: false },
        is_active: { type: Boolean, default: true },
        jwt_tokens: [{ type: String }],
        role_id: { type: Number, required: true },
        organization_id: { type: String, required: true },
        creation_timestamp: { 
            type: Date, 
            required: true, 
            default: () => new Date().toISOString() 
        }
    }
});

// ============================================================================
// USER TEST UTILITIES
// ============================================================================

export const setupUserTest = () => {
    const mockModel = createUserModelMock();
    const mockUser = createMockUser();
    const mockDocument = createMockUserDocument();
    
    return {
        mockModel,
        mockUser,
        mockDocument
    };
};

export const expectUserCreated = (mockModel: any, userData: any) => {
    expect(mockModel.create).toHaveBeenCalledWith(userData);
};

export const expectUserFound = (mockModel: any, query: any) => {
    expect(mockModel.findOne).toHaveBeenCalledWith(query);
};

export const expectUserUpdated = (mockModel: any, query: any, update: any) => {
    expect(mockModel.updateOne).toHaveBeenCalledWith(query, update);
};

export const expectUserDeleted = (mockModel: any, query: any) => {
    expect(mockModel.deleteOne).toHaveBeenCalledWith(query);
};

// ============================================================================
// USER VALIDATION HELPERS
// ============================================================================

export const createValidUserData = (overrides: any = {}) => ({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'securePassword123',
    role_id: 1,
    organization_id: '507f1f77bcf86cd799439012',
    ...overrides
});

export const createInvalidUserData = (field: string) => {
    const validData = createValidUserData();
    
    switch (field) {
        case 'email':
            return { ...validData, email: 'invalid-email' };
        case 'name':
            return { ...validData, name: '' };
        case 'password':
            return { ...validData, password: '' };
        case 'role_id':
            return { ...validData, role_id: null };
        case 'organization_id':
            return { ...validData, organization_id: '' };
        default:
            return validData;
    }
};

// ============================================================================
// CLEANUP
// ============================================================================

export const cleanupUserTest = () => {
    jest.clearAllMocks();
};
