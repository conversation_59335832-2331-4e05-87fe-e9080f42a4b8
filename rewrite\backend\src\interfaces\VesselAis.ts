import mongoose from "mongoose";
import { ILocation } from "./VesselLocation";

export interface IVesselAis {
    _id: mongoose.Types.ObjectId;
    location: ILocation;
    onboard_vessel_id: mongoose.Types.ObjectId;
    metadata: {
        message: Record<string, number | string | boolean>;
    };
    name: string;
    mmsi: string;
    timestamp: Date;
}

export interface IAisLookup {
    mmsi: string;
    collection: string;
    db: string;
    last_message_id: string;
    last_message_timestamp: Date;
    onboard_vessel_id: mongoose.Types.ObjectId;
}
