import { jest, describe, it, beforeEach, afterEach, expect } from '@jest/globals';

const mockJWT = {
    verify: jest.fn(),
    JsonWebTokenError: class JsonWebTokenError extends Error {
        constructor(message: string) {
            super(message);
            this.name = 'JsonWebTokenError';
        }
    }
};

const mockGetUser = jest.fn();
const mockApiKeyModel = { findOne: jest.fn() };

jest.doMock('jsonwebtoken', () => mockJWT);
jest.doMock('../../queries/User', () => ({ getUser: mockGetUser }));
jest.doMock('../../models/ApiKey', () => ({ default: mockApiKeyModel }));

describe('Auth Middleware - 100% Coverage Test', () => {
    let req: any;
    let res: any;
    let next: jest.Mock;
    let isAuthenticated: any;

    beforeEach(async () => {
        jest.clearAllMocks();

        req = {
            header: jest.fn(),
            method: 'GET',
            originalUrl: '/test',
            _endpoint_id: 'test-endpoint'
        };

        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis()
        };

        next = jest.fn();
        process.env.JWT_SECRET = 'test-jwt-secret';

        const authModule = await import('../../middlewares/auth');
        isAuthenticated = authModule.default;
    });

    afterEach(() => {
        jest.clearAllMocks();
        delete process.env.JWT_SECRET;
    });

    it('should return 401 if authorization header is missing', async () => {
        req.header.mockReturnValue(undefined);
        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if authorization header does not start with Bearer', async () => {
        req.header.mockReturnValue('Basic invalid-token');
        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token type must be Bearer' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if Bearer token is empty', async () => {
        req.header.mockReturnValue('Bearer ');
        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should authenticate user successfully with valid JWT token', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: false,
            jwt_tokens: ['valid-token']
        };

        mockJWT.verify.mockReturnValue({ user_id: mockUser._id });
        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(mockGetUser).toHaveBeenCalledWith({
            user_id: mockUser._id,
            includeUnprojected: true
        });
        expect(req.user).toEqual(mockUser);
        expect(next).toHaveBeenCalled();
    });

    it('should return 401 if user account is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: true,
            jwt_tokens: ['valid-token']
        };

        mockJWT.verify.mockReturnValue({ user_id: mockUser._id });
        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Your account has been deleted.' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if token is not available in user tokens', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = {
            _id: 'user123',
            name: 'Test User',
            email: '<EMAIL>',
            is_deleted: false,
            jwt_tokens: ['other-token']
        };

        mockJWT.verify.mockReturnValue({ user_id: mockUser._id });
        mockGetUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is not available.' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 500 for unexpected JWT response', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        mockJWT.verify.mockReturnValue({ unexpectedKey: 'value', user_id: undefined, api_key_id: undefined });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Unexpected error occured: JWT token returned unexpected data'
        });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 if JWT token is invalid (JsonWebTokenError)', async () => {
        req.header.mockReturnValue('Bearer invalid-token');
        mockJWT.verify.mockImplementation(() => {
            throw new mockJWT.JsonWebTokenError('Invalid token');
        });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 500 if JWT throws generic Error', async () => {
        req.header.mockReturnValue('Bearer invalid-token');
        mockJWT.verify.mockImplementation(() => {
            throw new Error('Generic JWT error');
        });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Unexpected error occured: Generic JWT error'
        });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 500 if JWT throws unknown error', async () => {
        req.header.mockReturnValue('Bearer invalid-token');
        mockJWT.verify.mockImplementation(() => {
            throw 'Unknown error type';
        });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Unexpected error occured: Unknown error type'
        });
        expect(next).not.toHaveBeenCalled();
    });

    it('should authenticate API key successfully', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint'],
            requests: 5,
            requests_endpoints: {} as any,
            last_used: new Date('2023-01-01'),
            save: jest.fn().mockResolvedValue({}),
            markModified: jest.fn(),
            toObject: jest.fn().mockReturnValue({ _id: 'apikey123', name: 'Test API Key' })
        };

        mockJWT.verify.mockReturnValue({ api_key_id: mockApiKey._id, user_id: undefined });
        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(mockApiKeyModel.findOne).toHaveBeenCalledWith({ _id: mockApiKey._id });
        expect(mockApiKey.requests).toBe(6);
        expect(mockApiKey.requests_endpoints['test-endpoint']).toBe(1);
        expect(mockApiKey.last_used).toBeInstanceOf(Date);
        expect(mockApiKey.markModified).toHaveBeenCalledWith('requests_endpoints');
        expect(mockApiKey.save).toHaveBeenCalled();
        expect(req.api_key).toEqual(mockApiKey.toObject());
        expect(next).toHaveBeenCalled();
    });

    it('should return 401 if API key is not found', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        mockJWT.verify.mockReturnValue({ api_key_id: 'invalid-key', user_id: undefined });
        mockApiKeyModel.findOne.mockResolvedValue(null);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'API key is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 400 if API key is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: true,
            is_revoked: false,
            allowed_endpoints: ['test-endpoint']
        };

        mockJWT.verify.mockReturnValue({ api_key_id: mockApiKey._id, user_id: undefined });
        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your API key has been removed. Please contact an administrator'
        });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 400 if API key is revoked', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: true,
            allowed_endpoints: ['test-endpoint']
        };

        mockJWT.verify.mockReturnValue({ api_key_id: mockApiKey._id, user_id: undefined });
        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your access has been revoked. Please contact an administrator'
        });
        expect(next).not.toHaveBeenCalled();
    });

    it('should return 403 if API key does not allow access to endpoint', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apikey123',
            is_deleted: false,
            is_revoked: false,
            allowed_endpoints: ['other-endpoint']
        };

        mockJWT.verify.mockReturnValue({ api_key_id: mockApiKey._id, user_id: undefined });
        mockApiKeyModel.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.json).toHaveBeenCalledWith({ message: 'You cannot access this resource' });
        expect(next).not.toHaveBeenCalled();
    });
});