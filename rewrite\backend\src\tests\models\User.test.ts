import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockIoEmitter = {
    emit: jest.fn()
};

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('User Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('../../utils/timezonesList', () => ({ defaultDateTimeFormat: 'MM/DD/YYYY HH:mm:ss' }));
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/User')];

        const UserModule = await import('../../models/User');
        const User = UserModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('User', expect.any(Object));
        expect(User).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        const creationTimestampField = schemaArg.paths.creation_timestamp;
        expect(creationTimestampField.default).toBeDefined();
        const timestamp = creationTimestampField.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.index).toHaveBeenCalledWith({ email: 1, username: 1 }, { unique: true });

        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockUser = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', name: 'Test User' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockUser);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockUser);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalledTimes(2);
        expect(mockIoEmitter.emit).toHaveBeenNthCalledWith(1, "notifyAll", {
            name: "users/changed",
            data: { _id: 'test-id', name: 'Test User' }
        });
        expect(mockIoEmitter.emit).toHaveBeenNthCalledWith(2, "notifyAll", {
            name: "users/changed",
            data: { _id: 'test-id', name: 'Test User' }
        });
    });

});
