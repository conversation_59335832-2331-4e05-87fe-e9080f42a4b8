const artifactsList = [
    {
        "_id": "66fa86dc64ecc5217496b976",
        "timestamp": "2024-08-20T01:17:51.524Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T01:17:51.524Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.7904566,
                11.216718
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa86f364ecc5217496b977",
        "timestamp": "2024-08-20T01:21:03.319Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T01:21:03.319Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.7818964,
                11.2137636
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa86f364ecc5217496b978",
        "timestamp": "2024-08-20T01:21:33.382Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T01:21:33.382Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.78057709999999,
                11.213244099999999
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa86f364ecc5217496b97b",
        "timestamp": "2024-08-20T02:47:29.479Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:47:29.479Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5593153,
                11.111305399999999
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military ship based on its structure and color."
    },
    {
        "_id": "66fa86fb64ecc5217496b97c",
        "timestamp": "2024-08-20T02:48:36.611Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:48:36.611Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5564149,
                11.109898
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa86fb64ecc5217496b97d",
        "timestamp": "2024-08-20T02:49:13.219Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:49:13.219Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5548417,
                11.1091429
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military ship with radar and communication equipment."
    },
    {
        "_id": "66fa86fb64ecc5217496b97e",
        "timestamp": "2024-08-20T02:50:37.248Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:50:37.248Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.55116939999999,
                11.107382
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "Large",
        "others": null
    },
    {
        "_id": "66fa86fb64ecc5217496b97f",
        "timestamp": "2024-08-20T02:52:46.510Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:52:46.510Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5454584,
                11.1046669
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa86fb64ecc5217496b980",
        "timestamp": "2024-08-20T02:53:56.680Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:53:56.680Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5423885,
                11.1032212
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a naval destroyer with stealth features."
    },
    {
        "_id": "66fa870664ecc5217496b981",
        "timestamp": "2024-08-20T02:54:53.418Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:54:53.418Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.539931,
                11.102011599999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa870664ecc5217496b982",
        "timestamp": "2024-08-20T02:55:56.412Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:55:56.412Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5371053,
                11.100667
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with advanced radar and communication equipment."
    },
    {
        "_id": "66fa870664ecc5217496b983",
        "timestamp": "2024-08-20T02:56:59.432Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:56:59.432Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5343334,
                11.0993356
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "Large",
        "others": null
    },
    {
        "_id": "66fa870664ecc5217496b984",
        "timestamp": "2024-08-20T02:57:31.392Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:57:31.392Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5329543,
                11.098653899999999
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa870664ecc5217496b985",
        "timestamp": "2024-08-20T02:58:28.945Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:58:28.945Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5304479,
                11.0973902
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to have a structure typical of military ships, such as a destroyer or frigate."
    },
    {
        "_id": "66fa871064ecc5217496b986",
        "timestamp": "2024-08-20T02:59:32.965Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T02:59:32.965Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5277162,
                11.0960472
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": "The vessel appears to be a military warship with a stealth design."
    },
    {
        "_id": "66fa871064ecc5217496b987",
        "timestamp": "2024-08-20T03:00:05.952Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:00:05.952Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.52629239999999,
                11.095341699999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871064ecc5217496b989",
        "timestamp": "2024-08-20T03:01:50.558Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:01:50.558Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5217193,
                11.093186699999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871064ecc5217496b98a",
        "timestamp": "2024-08-20T03:02:23.775Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:02:23.775Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5202944,
                11.092511199999999
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871864ecc5217496b98b",
        "timestamp": "2024-08-20T03:03:02.837Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:03:02.837Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.51862399999999,
                11.091718799999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871864ecc5217496b98c",
        "timestamp": "2024-08-20T03:04:48.736Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:04:48.736Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5140308,
                11.0895421
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a modern military frigate with advanced radar systems."
    },
    {
        "_id": "66fa871864ecc5217496b98d",
        "timestamp": "2024-08-20T03:06:51.977Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:06:51.977Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.50870309999999,
                11.0869673
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871864ecc5217496b98e",
        "timestamp": "2024-08-20T03:07:55.471Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:07:55.471Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5059223,
                11.0856174
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871864ecc5217496b98f",
        "timestamp": "2024-08-20T03:08:30.836Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:08:30.836Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5044051,
                11.0848873
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871c64ecc5217496b990",
        "timestamp": "2024-08-20T03:09:01.992Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:09:01.992Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.5030077,
                11.084237199999999
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military ship with a stealthy design."
    },
    {
        "_id": "66fa871c64ecc5217496b991",
        "timestamp": "2024-08-20T03:09:32.441Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:09:32.441Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.50171519999999,
                11.0835981
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with a stealth design."
    },
    {
        "_id": "66fa871c64ecc5217496b992",
        "timestamp": "2024-08-20T03:10:02.463Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:10:02.463Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.50042479999999,
                11.0829626
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871c64ecc5217496b993",
        "timestamp": "2024-08-20T03:10:37.881Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:10:37.881Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.498943,
                11.0821759
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa871c64ecc5217496b994",
        "timestamp": "2024-08-20T03:11:50.382Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:11:50.382Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4957951,
                11.0805929
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872264ecc5217496b995",
        "timestamp": "2024-08-20T03:12:22.946Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:12:22.946Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4944215,
                11.0799103
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872264ecc5217496b996",
        "timestamp": "2024-08-20T03:13:16.983Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:13:16.983Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.49207799999999,
                11.078796299999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with typical features such as radar systems and armaments."
    },
    {
        "_id": "66fa872264ecc5217496b997",
        "timestamp": "2024-08-20T03:13:47.031Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:13:47.031Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4907832,
                11.078166
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872264ecc5217496b998",
        "timestamp": "2024-08-20T03:14:17.326Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:14:17.326Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.489476,
                11.077538899999999
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872264ecc5217496b999",
        "timestamp": "2024-08-20T03:15:49.805Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:15:49.805Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4854239,
                11.0755891
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with visible radar and communication equipment."
    },
    {
        "_id": "66fa872a64ecc5217496b99a",
        "timestamp": "2024-08-20T03:16:53.425Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:16:53.425Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.48265239999999,
                11.0742327
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872a64ecc5217496b99b",
        "timestamp": "2024-08-20T03:17:31.935Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:17:31.935Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4810095,
                11.073431699999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872a64ecc5217496b99c",
        "timestamp": "2024-08-20T03:18:01.971Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:18:01.971Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.479721,
                11.0727952
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872a64ecc5217496b99d",
        "timestamp": "2024-08-20T03:18:32.453Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:18:32.453Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4783799,
                11.07213
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa872a64ecc5217496b99e",
        "timestamp": "2024-08-20T03:19:02.691Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:19:02.691Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4770886,
                11.0715062
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a Zumwalt-class destroyer."
    },
    {
        "_id": "66fa873a64ecc5217496b99f",
        "timestamp": "2024-08-20T03:19:32.802Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:19:32.802Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.47581179999999,
                11.0708547
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military frigate with advanced radar systems and communication equipment."
    },
    {
        "_id": "66fa873a64ecc5217496b9a0",
        "timestamp": "2024-08-20T03:34:19.877Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:34:19.877Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4361653,
                11.055262899999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa873a64ecc5217496b9a1",
        "timestamp": "2024-08-20T03:35:49.680Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:35:49.680Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.43215,
                11.053720199999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": "The vessel appears to be a modern military destroyer with advanced radar and communication equipment."
    },
    {
        "_id": "66fa873a64ecc5217496b9a2",
        "timestamp": "2024-08-20T03:37:27.661Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:37:27.661Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4278494,
                11.0518821
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa873a64ecc5217496b9a3",
        "timestamp": "2024-08-20T03:37:57.662Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:37:57.662Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.42653879999999,
                11.0513125
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military frigate with radar and communication equipment visible."
    },
    {
        "_id": "66fa874164ecc5217496b9a4",
        "timestamp": "2024-08-20T03:38:29.987Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:38:29.987Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.42513609999999,
                11.0507197
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa874164ecc5217496b9a5",
        "timestamp": "2024-08-20T03:39:00.079Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:39:00.079Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4238232,
                11.0501445
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa874164ecc5217496b9a6",
        "timestamp": "2024-08-20T03:40:34.606Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:40:34.606Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.41968519999999,
                11.0483476
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa874164ecc5217496b9a7",
        "timestamp": "2024-08-20T03:41:04.865Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:41:04.865Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4183798,
                11.0477942
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a naval warship with advanced radar systems."
    },
    {
        "_id": "66fa874164ecc5217496b9a8",
        "timestamp": "2024-08-20T03:41:34.893Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:41:34.893Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.4170805,
                11.047243499999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": "The vessel appears to be equipped with advanced radar and communication systems."
    },
    {
        "_id": "66fa875164ecc5217496b9a9",
        "timestamp": "2024-08-20T03:42:04.957Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:42:04.957Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.41578299999999,
                11.046688
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with radar and communication equipment visible."
    },
    {
        "_id": "66fa875164ecc5217496b9aa",
        "timestamp": "2024-08-20T03:42:34.962Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:42:34.962Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.414493,
                11.046119599999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer, characterized by its sleek design and possible radar systems."
    },
    {
        "_id": "66fa875164ecc5217496b9ab",
        "timestamp": "2024-08-20T03:43:05.067Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:43:05.067Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.41318299999999,
                11.045602299999999
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875164ecc5217496b9ac",
        "timestamp": "2024-08-20T03:51:18.273Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:51:18.273Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.392375,
                11.0352874
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": "The vessel appears to be a military destroyer with advanced radar and communication equipment."
    },
    {
        "_id": "66fa875164ecc5217496b9ad",
        "timestamp": "2024-08-20T03:55:22.020Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:55:22.020Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3820973,
                11.0302647
            ]
        },
        "category": "Warship",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875b64ecc5217496b9ae",
        "timestamp": "2024-08-20T03:57:56.061Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:57:56.061Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.37553039999999,
                11.0270789
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875b64ecc5217496b9af",
        "timestamp": "2024-08-20T03:59:22.576Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:59:22.576Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3718473,
                11.0252523
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875b64ecc5217496b9b0",
        "timestamp": "2024-08-20T03:59:54.887Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T03:59:54.887Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3704889,
                11.024601899999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875b64ecc5217496b9b1",
        "timestamp": "2024-08-20T04:13:58.673Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:13:58.673Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3306465,
                11.024428799999999
            ]
        },
        "category": "Frigate",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa875b64ecc5217496b9b2",
        "timestamp": "2024-08-20T04:15:06.228Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:15:06.228Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.32735389999999,
                11.024788299999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa879064ecc5217496b9b3",
        "timestamp": "2024-08-20T04:15:39.885Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:15:39.885Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.325676,
                11.0249619
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa879064ecc5217496b9b4",
        "timestamp": "2024-08-20T04:16:37.475Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:16:37.475Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3228243,
                11.0252871
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa879064ecc5217496b9b5",
        "timestamp": "2024-08-20T04:17:47.393Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:17:47.393Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3194187,
                11.0257086
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa879064ecc5217496b9b6",
        "timestamp": "2024-08-20T04:18:48.375Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-0_2024-08-20T04:18:48.375Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.3163756,
                11.0260803
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa879064ecc5217496b9b7",
        "timestamp": "2024-08-18T20:52:18.770Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-18T20:52:18.770Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.31839029999999,
                9.5684398
            ]
        },
        "category": "Hospital Ship",
        "super_category": "Special Craft",
        "color": "White with red crosses",
        "size": "large",
        "others": "The vessel is a hospital ship, identifiable by the red crosses on a white background."
    },
    {
        "_id": "66fa881164ecc5217496b9b8",
        "timestamp": "2024-08-19T00:01:18.340Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:01:18.340Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0250336,
                10.2672429
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881664ecc5217496b9b9",
        "timestamp": "2024-08-19T00:02:32.743Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:02:32.743Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.024355,
                10.2706267
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881664ecc5217496b9ba",
        "timestamp": "2024-08-19T00:03:02.801Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:03:02.801Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0240656,
                10.2719806
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881664ecc5217496b9bb",
        "timestamp": "2024-08-19T00:03:32.823Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:03:32.823Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.02378189999999,
                10.2733476
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881664ecc5217496b9bc",
        "timestamp": "2024-08-19T00:04:02.854Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:04:02.854Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0235081,
                10.274719
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881664ecc5217496b9bd",
        "timestamp": "2024-08-19T00:04:32.944Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:04:32.944Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.023232,
                10.2760839
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881c64ecc5217496b9be",
        "timestamp": "2024-08-19T00:05:03.036Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:05:03.036Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.02296299999999,
                10.277472399999999
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881c64ecc5217496b9bf",
        "timestamp": "2024-08-19T00:05:33.136Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:05:33.136Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0227017,
                10.2788579
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881c64ecc5217496b9c0",
        "timestamp": "2024-08-19T00:06:03.242Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:06:03.242Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0224191,
                10.2802554
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881c64ecc5217496b9c1",
        "timestamp": "2024-08-19T00:06:33.288Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:06:33.288Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0221377,
                10.2816564
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa881c64ecc5217496b9c2",
        "timestamp": "2024-08-19T00:07:03.291Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:07:03.291Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.02185619999999,
                10.2830631
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa885a64ecc5217496b9c3",
        "timestamp": "2024-08-19T00:07:33.381Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:07:33.381Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.02157059999999,
                10.2844704
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa885a64ecc5217496b9c4",
        "timestamp": "2024-08-19T00:08:03.476Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:08:03.476Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.02129529999999,
                10.2858772
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa885a64ecc5217496b9c5",
        "timestamp": "2024-08-19T00:08:33.531Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:08:33.531Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0210177,
                10.2873345
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa885a64ecc5217496b9c6",
        "timestamp": "2024-08-19T00:09:03.603Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-17T00:54:50.232Z/image/prototype-32_cam-1_2024-08-19T00:09:03.603Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                116.0207325,
                10.2887513
            ]
        },
        "category": "Destroyer",
        "super_category": "Military",
        "color": "Grey",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e1059590a2f21eaa5d8",
        "timestamp": "2024-08-20T07:28:53.758Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T07:28:53.758Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2691469,
                11.057528999999999
            ]
        },
        "category": "Inflatable Boat",
        "super_category": "Pleasure Craft",
        "color": "Orange and Black",
        "size": "small",
        "others": "The vessel appears to be a small inflatable boat with an outboard motor."
    },
    {
        "_id": "66fa8e1059590a2f21eaa5d9",
        "timestamp": "2024-08-20T08:22:12.728Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:22:12.728Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269514,
                11.057483
            ]
        },
        "category": "Container Ship",
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e1159590a2f21eaa5da",
        "timestamp": "2024-08-20T08:23:54.191Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:23:54.191Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695076,
                11.0574836
            ]
        },
        "category": "Inflatable Boat",
        "super_category": "Pleasure Craft",
        "color": "Gray with dark stripes",
        "size": "small",
        "others": "The vessel is an inflatable boat with people wearing life jackets."
    },
    {
        "_id": "66fa8e1159590a2f21eaa5dc",
        "timestamp": "2024-08-20T08:29:23.164Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:29:23.164Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951539999999,
                11.0574383
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Gray and green",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e1859590a2f21eaa5dd",
        "timestamp": "2024-08-20T08:29:54.035Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:29:54.035Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951509999999,
                11.0574396
            ]
        },
        "category": "Rigid Inflatable Boat (RIB)",
        "super_category": "Pleasure Craft",
        "color": "Dark with white text",
        "size": "small",
        "others": "The vessel appears to be a police RIB, likely used for patrol or rescue operations."
    },
    {
        "_id": "66fa8e1859590a2f21eaa5de",
        "timestamp": "2024-08-20T08:29:54.035Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:29:54.035Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951509999999,
                11.0574396
            ]
        },
        "category": null,
        "super_category": "Fishing",
        "color": "Dark with some greenish tones",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e1859590a2f21eaa5df",
        "timestamp": "2024-08-20T08:30:24.041Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:30:24.041Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695268,
                11.0574418
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "White and red",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e1859590a2f21eaa5e0",
        "timestamp": "2024-08-20T08:30:24.041Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:30:24.041Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695268,
                11.0574418
            ]
        },
        "category": "Inflatable Boat",
        "super_category": "Pleasure Craft",
        "color": "Gray with orange life jackets",
        "size": "small",
        "others": "The vessel is equipped with a Yamaha outboard motor."
    },
    {
        "_id": "66fa8e1859590a2f21eaa5e3",
        "timestamp": "2024-08-20T08:32:25.589Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:32:25.589Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26953689999999,
                11.0574665
            ]
        },
        "category": "Rigid Inflatable Boat (RIB)",
        "super_category": "Pleasure Craft",
        "color": "Dark gray with orange life vests",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8e1859590a2f21eaa5e4",
        "timestamp": "2024-08-20T08:32:25.589Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:32:25.589Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26953689999999,
                11.0574665
            ]
        },
        "category": "Rigid Inflatable Boat",
        "super_category": "Pleasure Craft",
        "color": "Dark hull with orange life jackets",
        "size": "small",
        "others": "The vessel is a small inflatable boat with people wearing life jackets."
    },
    {
        "_id": "66fa8e1859590a2f21eaa5e5",
        "timestamp": "2024-08-20T08:32:25.589Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:32:25.589Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26953689999999,
                11.0574665
            ]
        },
        "category": "Sailing Ship",
        "super_category": "Pleasure Craft",
        "color": "Brown and white with orange sails",
        "size": "small",
        "others": "The vessel appears to be a model or a small replica."
    },
    {
        "_id": "66fa8e1e59590a2f21eaa5e6",
        "timestamp": "2024-08-20T08:32:55.666Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:32:55.666Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269525,
                11.057470499999999
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Special Craft",
        "color": "White and black with orange life jackets",
        "size": "small",
        "others": "The vessel has people wearing life jackets."
    },
    {
        "_id": "66fa8e1e59590a2f21eaa5e7",
        "timestamp": "2024-08-20T08:32:55.666Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:32:55.666Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269525,
                11.057470499999999
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White and dark grey",
        "size": "small",
        "others": "The vessel is a patrol boat with coast guard personnel on board."
    },
    {
        "_id": "66fa8e1e59590a2f21eaa5e8",
        "timestamp": "2024-08-20T08:33:25.758Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:33:25.758Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951989999999,
                11.0574807
            ]
        },
        "category": "Rigid Inflatable Boat",
        "super_category": "Pleasure Craft",
        "color": "Dark gray with orange life vests",
        "size": "small",
        "others": "The boat is equipped with an outboard motor and life vests are worn by passengers."
    },
    {
        "_id": "66fa8e1e59590a2f21eaa5e9",
        "timestamp": "2024-08-20T08:36:59.453Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:36:59.453Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695129,
                11.0574727
            ]
        },
        "category": "Coast Guard Boat",
        "super_category": "Special Craft",
        "color": "Dark hull with white superstructure",
        "size": "small",
        "others": "The image shows a Philippine Coast Guard vessel and personnel."
    },
    {
        "_id": "66fa8e1e59590a2f21eaa5ea",
        "timestamp": "2024-08-20T08:37:29.568Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:37:29.568Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695119,
                11.057473499999999
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White hull with black and red accents",
        "size": "medium",
        "others": "Personnel visible on the vessel, possibly indicating a patrol or law enforcement role."
    },
    {
        "_id": "66fa8e2459590a2f21eaa5ed",
        "timestamp": "2024-08-20T08:39:00.672Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:39:00.672Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.0574797
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Green and brown",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e2459590a2f21eaa5ee",
        "timestamp": "2024-08-20T08:50:16.468Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T08:50:16.468Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694502,
                11.0574896
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "Dark with orange elements",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8e2459590a2f21eaa5f0",
        "timestamp": "2024-08-20T09:24:14.239Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T09:24:14.239Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694818,
                11.0574747
            ]
        },
        "category": "Container Ship",
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e2f59590a2f21eaa5f1",
        "timestamp": "2024-08-20T10:02:50.315Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T10:02:50.315Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2693833,
                11.0575104
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "Dark blue with white and yellow stripes",
        "size": "medium",
        "others": "The vessel has multiple antennas and radar equipment visible on top."
    },
    {
        "_id": "66fa8e2f59590a2f21eaa5f2",
        "timestamp": "2024-08-20T10:02:50.315Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T10:02:50.315Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2693833,
                11.0575104
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Special Craft",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e3459590a2f21eaa5f7",
        "timestamp": "2024-08-20T10:27:12.220Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T10:27:12.220Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694569,
                11.057489
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Dark gray",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e3459590a2f21eaa5fa",
        "timestamp": "2024-08-20T10:31:46.434Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-0_2024-08-20T10:31:46.434Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694441,
                11.0575082
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White",
        "size": "small",
        "others": "The vessel appears to be a small utility or service craft, possibly used for specific operations or maintenance."
    },
    {
        "_id": "66fa8e5459590a2f21eaa5fd",
        "timestamp": "2024-08-20T07:12:51.393Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:12:51.393Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2692977,
                11.057564099999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with black markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e5459590a2f21eaa5fe",
        "timestamp": "2024-08-20T07:13:21.494Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:13:21.494Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2692914,
                11.057563199999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue markings",
        "size": "medium",
        "others": "The vessel belongs to the Philippine Coast Guard."
    },
    {
        "_id": "66fa8e5459590a2f21eaa5ff",
        "timestamp": "2024-08-20T07:13:51.520Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:13:51.520Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26926739999999,
                11.0575657
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e5459590a2f21eaa600",
        "timestamp": "2024-08-20T07:13:51.520Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:13:51.520Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26926739999999,
                11.0575657
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Brown and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e5459590a2f21eaa601",
        "timestamp": "2024-08-20T07:14:21.591Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:14:21.591Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26924419999999,
                11.0575583
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e7a59590a2f21eaa602",
        "timestamp": "2024-08-20T07:47:43.954Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:47:43.954Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26925879999999,
                11.0575709
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e7a59590a2f21eaa603",
        "timestamp": "2024-08-20T07:48:14.015Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:48:14.015Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2692512,
                11.0575668
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e7a59590a2f21eaa604",
        "timestamp": "2024-08-20T07:48:44.050Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:48:44.050Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2692433,
                11.057565799999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e7a59590a2f21eaa605",
        "timestamp": "2024-08-20T07:49:14.097Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:49:14.097Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26922789999999,
                11.057562599999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and rust",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e7a59590a2f21eaa606",
        "timestamp": "2024-08-20T07:55:14.409Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:55:14.409Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26930779999999,
                11.0575611
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "Black and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8859590a2f21eaa607",
        "timestamp": "2024-08-20T07:58:44.733Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:58:44.733Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269337,
                11.0575674
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "Black and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8859590a2f21eaa608",
        "timestamp": "2024-08-20T07:59:14.761Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:59:14.761Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2693401,
                11.0575675
            ]
        },
        "category": "Tug Boat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8859590a2f21eaa609",
        "timestamp": "2024-08-20T07:59:44.860Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T07:59:44.860Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269341,
                11.0575742
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "blue and red",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e8859590a2f21eaa60a",
        "timestamp": "2024-08-20T08:00:14.887Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:00:14.887Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2693429,
                11.057573399999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8859590a2f21eaa60b",
        "timestamp": "2024-08-20T08:04:45.317Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:04:45.317Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26941649999999,
                11.057538
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8d59590a2f21eaa60c",
        "timestamp": "2024-08-20T08:05:15.410Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:05:15.410Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26942509999999,
                11.0575336
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e8d59590a2f21eaa60d",
        "timestamp": "2024-08-20T08:05:45.528Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:05:45.528Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269438,
                11.0575382
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8d59590a2f21eaa60e",
        "timestamp": "2024-08-20T08:06:15.618Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:06:15.618Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694417,
                11.0575447
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8d59590a2f21eaa60f",
        "timestamp": "2024-08-20T08:06:15.618Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:06:15.618Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694417,
                11.0575447
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "white with blue tarp",
        "size": "medium",
        "others": "The vessel has a unique identification number '4411' and appears to be a specialized craft, possibly for research or utility purposes."
    },
    {
        "_id": "66fa8e8d59590a2f21eaa610",
        "timestamp": "2024-08-20T08:06:45.726Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:06:45.726Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26943519999999,
                11.057547399999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e8d59590a2f21eaa612",
        "timestamp": "2024-08-20T08:07:15.731Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:07:15.731Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269421,
                11.057548299999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9359590a2f21eaa613",
        "timestamp": "2024-08-20T08:07:45.796Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:07:45.796Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26942369999999,
                11.057551199999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9359590a2f21eaa615",
        "timestamp": "2024-08-20T08:08:15.805Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:08:15.805Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694422,
                11.0575464
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9359590a2f21eaa616",
        "timestamp": "2024-08-20T08:08:45.872Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:08:45.872Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26944239999999,
                11.0575409
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9359590a2f21eaa617",
        "timestamp": "2024-08-20T08:09:15.959Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:09:15.959Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694509,
                11.057535699999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9359590a2f21eaa618",
        "timestamp": "2024-08-20T08:09:46.010Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:09:46.010Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694485,
                11.0575269
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9559590a2f21eaa619",
        "timestamp": "2024-08-20T08:10:16.015Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:10:16.015Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694456,
                11.0575271
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": "The vessel is docked alongside another vessel."
    },
    {
        "_id": "66fa8e9559590a2f21eaa61a",
        "timestamp": "2024-08-20T08:10:46.098Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:10:46.098Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694447,
                11.0575256
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and rust-colored",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9559590a2f21eaa61b",
        "timestamp": "2024-08-20T08:11:16.116Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:11:16.116Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26945149999999,
                11.0575231
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9559590a2f21eaa61c",
        "timestamp": "2024-08-20T08:11:46.227Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:11:46.227Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26945769999999,
                11.0575133
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9559590a2f21eaa61d",
        "timestamp": "2024-08-20T08:12:16.299Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:12:16.299Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947059999999,
                11.0575098
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and rust-colored",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9c59590a2f21eaa61e",
        "timestamp": "2024-08-20T08:12:46.322Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:12:46.322Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947799999999,
                11.057513
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9c59590a2f21eaa61f",
        "timestamp": "2024-08-20T08:13:16.434Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:13:16.434Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694827,
                11.0575142
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9c59590a2f21eaa621",
        "timestamp": "2024-08-20T08:13:46.519Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:13:46.519Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269486,
                11.0575099
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8e9c59590a2f21eaa622",
        "timestamp": "2024-08-20T08:14:16.639Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:14:16.639Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694918,
                11.0575124
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8e9c59590a2f21eaa623",
        "timestamp": "2024-08-20T08:14:46.701Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:14:46.701Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694918,
                11.057508799999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ea159590a2f21eaa624",
        "timestamp": "2024-08-20T08:15:16.711Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:15:16.711Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26949289999999,
                11.0575072
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea159590a2f21eaa625",
        "timestamp": "2024-08-20T08:15:46.743Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:15:46.743Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694913,
                11.0575095
            ]
        },
        "category": null,
        "super_category": "Tanker",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ea159590a2f21eaa626",
        "timestamp": "2024-08-20T08:16:16.767Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:16:16.767Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948709999999,
                11.0575016
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea159590a2f21eaa627",
        "timestamp": "2024-08-20T08:16:46.802Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:16:46.802Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948949999999,
                11.0575039
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white with some rust",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea159590a2f21eaa628",
        "timestamp": "2024-08-20T08:17:16.859Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:17:16.859Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950269999999,
                11.0575083
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa629",
        "timestamp": "2024-08-20T08:17:46.948Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:17:46.948Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951179999999,
                11.0575029
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and red with a white superstructure",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa62a",
        "timestamp": "2024-08-20T08:18:17.029Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:18:17.029Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695181,
                11.057495099999999
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White with black and blue accents",
        "size": "medium",
        "others": "The vessel has a unique identifier '4441' and appears to be a special purpose craft, possibly for research or patrol."
    },
    {
        "_id": "66fa8ea859590a2f21eaa62b",
        "timestamp": "2024-08-20T08:18:17.029Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:18:17.029Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695181,
                11.057495099999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Rusty red and blue",
        "size": "large",
        "others": "The vessel appears to be docked and shows signs of wear and rust."
    },
    {
        "_id": "66fa8ea859590a2f21eaa62c",
        "timestamp": "2024-08-20T08:18:47.082Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:18:47.082Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695186,
                11.0574824
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with black and blue markings",
        "size": "Medium",
        "others": "The vessel has a patrol vessel design with visible equipment on deck."
    },
    {
        "_id": "66fa8ea859590a2f21eaa62d",
        "timestamp": "2024-08-20T08:18:47.082Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:18:47.082Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695186,
                11.0574824
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa62e",
        "timestamp": "2024-08-20T08:19:17.211Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:19:17.211Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695134,
                11.0574655
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa62f",
        "timestamp": "2024-08-20T08:19:17.211Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:19:17.211Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695134,
                11.0574655
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa630",
        "timestamp": "2024-08-20T08:19:47.303Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:19:47.303Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695128,
                11.0574602
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with black and red markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ea859590a2f21eaa631",
        "timestamp": "2024-08-20T08:19:47.303Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:19:47.303Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695128,
                11.0574602
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa632",
        "timestamp": "2024-08-20T08:20:17.391Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:20:17.391Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951919999999,
                11.057463499999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with blue markings",
        "size": "medium",
        "others": "The vessel is associated with the Philippine Coast Guard."
    },
    {
        "_id": "66fa8eac59590a2f21eaa633",
        "timestamp": "2024-08-20T08:20:17.391Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:20:17.391Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951919999999,
                11.057463499999999
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white with rust patches",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa635",
        "timestamp": "2024-08-20T08:20:47.480Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:20:47.480Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695165,
                11.057475799999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa636",
        "timestamp": "2024-08-20T08:20:47.480Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:20:47.480Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695165,
                11.057475799999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa637",
        "timestamp": "2024-08-20T08:21:17.663Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:21:17.663Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695222,
                11.0574892
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa638",
        "timestamp": "2024-08-20T08:21:17.663Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:21:17.663Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695222,
                11.0574892
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eac59590a2f21eaa639",
        "timestamp": "2024-08-20T08:21:47.764Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:21:47.764Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951989999999,
                11.0574903
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is a coast guard ship, likely used for patrol and rescue operations."
    },
    {
        "_id": "66fa8eac59590a2f21eaa63b",
        "timestamp": "2024-08-20T08:22:17.839Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:22:17.839Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695129,
                11.057480199999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is marked with the number 4411 and belongs to the Philippine Coast Guard."
    },
    {
        "_id": "66fa8eac59590a2f21eaa63c",
        "timestamp": "2024-08-20T08:22:17.839Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:22:17.839Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695129,
                11.057480199999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white with blue accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa63d",
        "timestamp": "2024-08-20T08:22:47.905Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:22:47.905Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695146,
                11.0574775
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is a coast guard ship with a small boat alongside."
    },
    {
        "_id": "66fa8eb059590a2f21eaa63e",
        "timestamp": "2024-08-20T08:22:47.905Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:22:47.905Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695146,
                11.0574775
            ]
        },
        "category": "Tanker",
        "super_category": "Tanker",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa63f",
        "timestamp": "2024-08-20T08:23:17.914Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:23:17.914Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695143,
                11.057481
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is marked with the Philippine Coast Guard insignia."
    },
    {
        "_id": "66fa8eb059590a2f21eaa640",
        "timestamp": "2024-08-20T08:23:17.914Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:23:17.914Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695143,
                11.057481
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa641",
        "timestamp": "2024-08-20T08:23:47.946Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:23:47.946Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950769999999,
                11.057483
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa642",
        "timestamp": "2024-08-20T08:23:47.946Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:23:47.946Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950769999999,
                11.057483
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa643",
        "timestamp": "2024-08-20T08:24:18.027Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:24:18.027Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.057483399999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": "The vessel is part of the Philippine Coast Guard."
    },
    {
        "_id": "66fa8eb059590a2f21eaa644",
        "timestamp": "2024-08-20T08:24:18.027Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:24:18.027Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.057483399999999
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and white with blue accents",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa645",
        "timestamp": "2024-08-20T08:24:48.049Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:24:48.049Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695038,
                11.057481899999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb059590a2f21eaa646",
        "timestamp": "2024-08-20T08:24:48.049Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:24:48.049Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695038,
                11.057481899999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa647",
        "timestamp": "2024-08-20T08:25:18.112Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:25:18.112Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950819999999,
                11.05749
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is marked with 'PHILIPPINE COAST' and the number '4411'. It is a coast guard vessel."
    },
    {
        "_id": "66fa8eb759590a2f21eaa648",
        "timestamp": "2024-08-20T08:25:18.112Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:25:18.112Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950819999999,
                11.05749
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa649",
        "timestamp": "2024-08-20T08:25:48.172Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:25:48.172Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695116,
                11.057493299999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with black text",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa64a",
        "timestamp": "2024-08-20T08:25:48.172Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:25:48.172Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695116,
                11.057493299999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa64b",
        "timestamp": "2024-08-20T08:26:18.244Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:26:18.244Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695157,
                11.057493299999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is part of the Philippine Coast Guard."
    },
    {
        "_id": "66fa8eb759590a2f21eaa64c",
        "timestamp": "2024-08-20T08:26:18.244Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:26:18.244Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695157,
                11.057493299999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and red with white superstructure",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa64d",
        "timestamp": "2024-08-20T08:26:48.276Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:26:48.276Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695203,
                11.0574817
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with black text",
        "size": "medium",
        "others": "The vessel is a coast guard ship with visible personnel on deck."
    },
    {
        "_id": "66fa8eb759590a2f21eaa64e",
        "timestamp": "2024-08-20T08:26:48.276Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:26:48.276Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695203,
                11.0574817
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa64f",
        "timestamp": "2024-08-20T08:27:18.372Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:27:18.372Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695201,
                11.0574728
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eb759590a2f21eaa650",
        "timestamp": "2024-08-20T08:27:18.372Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:27:18.372Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695201,
                11.0574728
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa651",
        "timestamp": "2024-08-20T08:27:48.395Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:27:48.395Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695186,
                11.057460299999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa652",
        "timestamp": "2024-08-20T08:27:48.395Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:27:48.395Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695186,
                11.057460299999999
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa653",
        "timestamp": "2024-08-20T08:28:18.479Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:28:18.479Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695193,
                11.0574551
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": "The vessel is marked with 'PHILIPPINE COAST GUARD' and the number '4411'."
    },
    {
        "_id": "66fa8ebd59590a2f21eaa654",
        "timestamp": "2024-08-20T08:28:18.479Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:28:18.479Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695193,
                11.0574551
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa655",
        "timestamp": "2024-08-20T08:28:48.502Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:28:48.502Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695183,
                11.0574494
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa656",
        "timestamp": "2024-08-20T08:28:48.502Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:28:48.502Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695183,
                11.0574494
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa657",
        "timestamp": "2024-08-20T08:29:18.530Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:29:18.530Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951609999999,
                11.0574392
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa658",
        "timestamp": "2024-08-20T08:29:18.530Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:29:18.530Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951609999999,
                11.0574392
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ebd59590a2f21eaa659",
        "timestamp": "2024-08-20T08:29:48.635Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:29:48.635Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695155,
                11.057438699999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with red, blue, and yellow stripes",
        "size": "medium",
        "others": "The vessel is a patrol craft used by the Philippine Coast Guard."
    },
    {
        "_id": "66fa8ebd59590a2f21eaa65a",
        "timestamp": "2024-08-20T08:29:48.635Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:29:48.635Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695155,
                11.057438699999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa65b",
        "timestamp": "2024-08-20T08:30:18.729Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:30:18.729Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695248,
                11.057443
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa65c",
        "timestamp": "2024-08-20T08:30:18.729Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:30:18.729Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695248,
                11.057443
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa65d",
        "timestamp": "2024-08-20T08:30:48.754Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:30:48.754Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695359,
                11.0574496
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": "The vessel is a patrol boat belonging to the Philippine Coast Guard."
    },
    {
        "_id": "66fa8ec159590a2f21eaa65e",
        "timestamp": "2024-08-20T08:30:48.754Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:30:48.754Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695359,
                11.0574496
            ]
        },
        "category": null,
        "super_category": "Tanker",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa65f",
        "timestamp": "2024-08-20T08:31:18.856Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:31:18.856Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26953689999999,
                11.0574532
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa660",
        "timestamp": "2024-08-20T08:31:18.856Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:31:18.856Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26953689999999,
                11.0574532
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa661",
        "timestamp": "2024-08-20T08:31:48.960Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:31:48.960Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695383,
                11.0574602
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is marked with the Philippine Coast Guard insignia."
    },
    {
        "_id": "66fa8ec159590a2f21eaa662",
        "timestamp": "2024-08-20T08:31:48.960Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:31:48.960Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695383,
                11.0574602
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec159590a2f21eaa663",
        "timestamp": "2024-08-20T08:32:18.961Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:32:18.961Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269538,
                11.0574668
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": "The vessel is marked with the number 4411."
    },
    {
        "_id": "66fa8ec159590a2f21eaa664",
        "timestamp": "2024-08-20T08:32:18.961Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:32:18.961Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269538,
                11.0574668
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec559590a2f21eaa665",
        "timestamp": "2024-08-20T08:32:48.961Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:32:48.961Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26952879999999,
                11.057471999999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec559590a2f21eaa666",
        "timestamp": "2024-08-20T08:32:48.961Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:32:48.961Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26952879999999,
                11.057471999999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa667",
        "timestamp": "2024-08-20T08:33:18.993Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:18.993Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695225,
                11.0574788
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa668",
        "timestamp": "2024-08-20T08:33:18.993Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:18.993Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695225,
                11.0574788
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa669",
        "timestamp": "2024-08-20T08:33:18.993Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:18.993Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695225,
                11.0574788
            ]
        },
        "category": "Barge",
        "super_category": "Cargo",
        "color": "Dark red",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa66a",
        "timestamp": "2024-08-20T08:33:49.027Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:49.027Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951439999999,
                11.05748
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa66b",
        "timestamp": "2024-08-20T08:33:49.027Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:49.027Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951439999999,
                11.05748
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and white with blue accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa66c",
        "timestamp": "2024-08-20T08:33:49.027Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:33:49.027Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951439999999,
                11.05748
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa66d",
        "timestamp": "2024-08-20T08:34:19.132Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:19.132Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695093,
                11.0574806
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": "The vessel appears to be a patrol boat with personnel on deck."
    },
    {
        "_id": "66fa8ec659590a2f21eaa66e",
        "timestamp": "2024-08-20T08:34:19.132Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:19.132Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695093,
                11.0574806
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa66f",
        "timestamp": "2024-08-20T08:34:19.132Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:19.132Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695093,
                11.0574806
            ]
        },
        "category": "Barge",
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa670",
        "timestamp": "2024-08-20T08:34:49.138Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:49.138Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.057486899999999
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with black and blue accents",
        "size": "medium",
        "others": "The vessel has a patrol boat design with people visible on deck."
    },
    {
        "_id": "66fa8ec659590a2f21eaa671",
        "timestamp": "2024-08-20T08:34:49.138Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:49.138Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.057486899999999
            ]
        },
        "category": "Landing Craft",
        "super_category": "Special Craft",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ec659590a2f21eaa672",
        "timestamp": "2024-08-20T08:34:49.138Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:34:49.138Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695052,
                11.057486899999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa673",
        "timestamp": "2024-08-20T08:35:19.202Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:35:19.202Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950819999999,
                11.0574871
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "medium",
        "others": "The vessel appears to be a coast guard or patrol vessel."
    },
    {
        "_id": "66fa8ecb59590a2f21eaa674",
        "timestamp": "2024-08-20T08:35:19.202Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:35:19.202Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950819999999,
                11.0574871
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa675",
        "timestamp": "2024-08-20T08:35:49.312Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:35:49.312Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695074,
                11.0574818
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": "The vessel appears to be a coast guard or patrol vessel."
    },
    {
        "_id": "66fa8ecb59590a2f21eaa676",
        "timestamp": "2024-08-20T08:35:49.312Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:35:49.312Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695074,
                11.0574818
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa677",
        "timestamp": "2024-08-20T08:35:49.312Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:35:49.312Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695074,
                11.0574818
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa678",
        "timestamp": "2024-08-20T08:36:19.343Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:36:19.343Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950629999999,
                11.057474
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is a coast guard ship with personnel visible on deck."
    },
    {
        "_id": "66fa8ecb59590a2f21eaa679",
        "timestamp": "2024-08-20T08:36:19.343Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:36:19.343Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26950629999999,
                11.057474
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa67a",
        "timestamp": "2024-08-20T08:36:49.352Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:36:49.352Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951269999999,
                11.0574701
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa67b",
        "timestamp": "2024-08-20T08:36:49.352Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:36:49.352Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951269999999,
                11.0574701
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa67c",
        "timestamp": "2024-08-20T08:37:19.466Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:37:19.466Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695116,
                11.0574744
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa67d",
        "timestamp": "2024-08-20T08:37:19.466Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:37:19.466Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695116,
                11.0574744
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white with blue accents",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ecb59590a2f21eaa67e",
        "timestamp": "2024-08-20T08:37:19.466Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:37:19.466Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695116,
                11.0574744
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa67f",
        "timestamp": "2024-08-20T08:37:49.552Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:37:49.552Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951229999999,
                11.0574671
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa680",
        "timestamp": "2024-08-20T08:37:49.552Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:37:49.552Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951229999999,
                11.0574671
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa681",
        "timestamp": "2024-08-20T08:38:19.656Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:19.656Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695155,
                11.0574693
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": "The vessel is a patrol craft used by the Philippine Coast Guard."
    },
    {
        "_id": "66fa8ecf59590a2f21eaa682",
        "timestamp": "2024-08-20T08:38:19.656Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:19.656Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695155,
                11.0574693
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and blue with white superstructure",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa683",
        "timestamp": "2024-08-20T08:38:19.656Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:19.656Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695155,
                11.0574693
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa684",
        "timestamp": "2024-08-20T08:38:49.743Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:49.743Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951229999999,
                11.0574747
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa685",
        "timestamp": "2024-08-20T08:38:49.743Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:49.743Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951229999999,
                11.0574747
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa686",
        "timestamp": "2024-08-20T08:38:49.743Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:38:49.743Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26951229999999,
                11.0574747
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa687",
        "timestamp": "2024-08-20T08:39:19.793Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:39:19.793Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694976,
                11.057487
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white with red and blue stripes",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa688",
        "timestamp": "2024-08-20T08:39:19.793Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:39:19.793Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694976,
                11.057487
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ecf59590a2f21eaa689",
        "timestamp": "2024-08-20T08:39:49.839Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:39:49.839Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26949309999999,
                11.0574969
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is marked with 'PHILIPPINE COAST' and the number '4411'. It is likely a patrol or coast guard vessel."
    },
    {
        "_id": "66fa8ecf59590a2f21eaa68a",
        "timestamp": "2024-08-20T08:39:49.839Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:39:49.839Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26949309999999,
                11.0574969
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed659590a2f21eaa68b",
        "timestamp": "2024-08-20T08:40:19.879Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:40:19.879Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947419999999,
                11.0575055
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": "The vessel has a visible emblem on the side."
    },
    {
        "_id": "66fa8ed659590a2f21eaa68c",
        "timestamp": "2024-08-20T08:40:19.879Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:40:19.879Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947419999999,
                11.0575055
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue with white accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed659590a2f21eaa68d",
        "timestamp": "2024-08-20T08:42:20.039Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:42:20.039Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694526,
                11.0575118
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8ed659590a2f21eaa68e",
        "timestamp": "2024-08-20T08:42:50.056Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:42:50.056Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694604,
                11.057502699999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed659590a2f21eaa68f",
        "timestamp": "2024-08-20T08:43:20.127Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:43:20.127Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694616,
                11.0574943
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed659590a2f21eaa690",
        "timestamp": "2024-08-20T08:43:50.174Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:43:50.174Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26946849999999,
                11.057484299999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": "The vessel appears to be docked at a port."
    },
    {
        "_id": "66fa8ed959590a2f21eaa692",
        "timestamp": "2024-08-20T08:45:20.248Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:45:20.248Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694736,
                11.0574567
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa693",
        "timestamp": "2024-08-20T08:45:50.281Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:45:50.281Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948569999999,
                11.0574669
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa694",
        "timestamp": "2024-08-20T08:46:20.351Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:46:20.351Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948519999999,
                11.057469699999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa695",
        "timestamp": "2024-08-20T08:46:20.351Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:46:20.351Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948519999999,
                11.057469699999999
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa696",
        "timestamp": "2024-08-20T08:46:50.443Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:46:50.443Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948639999999,
                11.0574742
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa697",
        "timestamp": "2024-08-20T08:47:20.546Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:20.546Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269489,
                11.0574794
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa698",
        "timestamp": "2024-08-20T08:47:20.546Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:20.546Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269489,
                11.0574794
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ed959590a2f21eaa699",
        "timestamp": "2024-08-20T08:47:20.546Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:20.546Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269489,
                11.0574794
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa69a",
        "timestamp": "2024-08-20T08:47:50.646Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:50.646Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948279999999,
                11.05748
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa69b",
        "timestamp": "2024-08-20T08:47:50.646Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:50.646Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948279999999,
                11.05748
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa69c",
        "timestamp": "2024-08-20T08:47:50.646Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:47:50.646Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948279999999,
                11.05748
            ]
        },
        "category": null,
        "super_category": "Tanker",
        "color": "Blue and red",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa69d",
        "timestamp": "2024-08-20T08:48:20.766Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:48:20.766Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694717,
                11.0574739
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": "The vessel is a coast guard ship with visible personnel on deck."
    },
    {
        "_id": "66fa8ede59590a2f21eaa69e",
        "timestamp": "2024-08-20T08:48:20.766Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:48:20.766Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694717,
                11.0574739
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Blue and red",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa69f",
        "timestamp": "2024-08-20T08:48:50.880Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:48:50.880Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947039999999,
                11.057480199999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "medium",
        "others": "The vessel is marked with the Philippine Coast Guard insignia."
    },
    {
        "_id": "66fa8ede59590a2f21eaa6a0",
        "timestamp": "2024-08-20T08:48:50.880Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:48:50.880Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947039999999,
                11.057480199999999
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa6a1",
        "timestamp": "2024-08-20T08:49:20.992Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:49:20.992Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269466,
                11.0574821
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa6a2",
        "timestamp": "2024-08-20T08:49:20.992Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:49:20.992Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269466,
                11.0574821
            ]
        },
        "category": "Supply Vessel",
        "super_category": "Special Craft",
        "color": "Red and blue",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa6a3",
        "timestamp": "2024-08-20T08:49:20.992Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:49:20.992Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269466,
                11.0574821
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8ede59590a2f21eaa6a4",
        "timestamp": "2024-08-20T08:49:51.057Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:49:51.057Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26945939999999,
                11.057487499999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef359590a2f21eaa6a6",
        "timestamp": "2024-08-20T08:50:21.058Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T08:50:21.058Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694483,
                11.05749
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and blue",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ef359590a2f21eaa6a7",
        "timestamp": "2024-08-20T09:00:22.359Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:00:22.359Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694259,
                11.0575151
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef359590a2f21eaa6a8",
        "timestamp": "2024-08-20T09:00:52.384Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:00:52.384Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26941319999999,
                11.057509399999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef359590a2f21eaa6a9",
        "timestamp": "2024-08-20T09:07:23.082Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:07:23.082Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694784,
                11.057469099999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8ef359590a2f21eaa6ab",
        "timestamp": "2024-08-20T09:07:53.207Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:07:53.207Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694877,
                11.0574754
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6ac",
        "timestamp": "2024-08-20T09:08:23.303Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:08:23.303Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694897,
                11.0574816
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": "The vessel has a visible identification number and partial text indicating it may belong to the Philippines."
    },
    {
        "_id": "66fa8ef759590a2f21eaa6ad",
        "timestamp": "2024-08-20T09:08:23.303Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:08:23.303Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694897,
                11.0574816
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and blue with white accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6ae",
        "timestamp": "2024-08-20T09:08:53.326Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:08:53.326Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694949,
                11.057491899999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is part of the Philippine Coast Guard."
    },
    {
        "_id": "66fa8ef759590a2f21eaa6af",
        "timestamp": "2024-08-20T09:08:53.326Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:08:53.326Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694949,
                11.057491899999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white with blue accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6b0",
        "timestamp": "2024-08-20T09:08:53.326Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:08:53.326Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694949,
                11.057491899999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6b1",
        "timestamp": "2024-08-20T09:09:23.335Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:09:23.335Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2695004,
                11.057495399999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6b2",
        "timestamp": "2024-08-20T09:09:53.351Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:09:53.351Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694932,
                11.0574836
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8ef759590a2f21eaa6b3",
        "timestamp": "2024-08-20T09:10:23.371Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:10:23.371Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26949309999999,
                11.0574848
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": "The vessel is part of the Philippine Coast Guard."
    },
    {
        "_id": "66fa8ef759590a2f21eaa6b4",
        "timestamp": "2024-08-20T09:10:23.371Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:10:23.371Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26949309999999,
                11.0574848
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Dark with red and white sections",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6b5",
        "timestamp": "2024-08-20T09:10:53.476Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:10:53.476Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694866,
                11.0574775
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "Medium",
        "others": "The vessel is marked with 'PHILIPPINE COAST GUARD' and the number '4411'."
    },
    {
        "_id": "66fa8eff59590a2f21eaa6b6",
        "timestamp": "2024-08-20T09:10:53.476Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:10:53.476Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694866,
                11.0574775
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Blue and red",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6b7",
        "timestamp": "2024-08-20T09:11:23.569Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:11:23.569Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694851,
                11.0574712
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with black markings",
        "size": "medium",
        "others": "The vessel is marked with 'PHILIPPINE COAST' and appears to be a coast guard vessel."
    },
    {
        "_id": "66fa8eff59590a2f21eaa6b8",
        "timestamp": "2024-08-20T09:11:53.583Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:11:53.583Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948259999999,
                11.0574493
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6b9",
        "timestamp": "2024-08-20T09:11:53.583Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:11:53.583Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948259999999,
                11.0574493
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Dark gray and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6ba",
        "timestamp": "2024-08-20T09:11:53.583Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:11:53.583Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948259999999,
                11.0574493
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6bc",
        "timestamp": "2024-08-20T09:12:23.694Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:12:23.694Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948189999999,
                11.0574532
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6bd",
        "timestamp": "2024-08-20T09:12:53.762Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:12:53.762Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694811,
                11.057454199999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8eff59590a2f21eaa6be",
        "timestamp": "2024-08-20T09:12:53.762Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:12:53.762Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694811,
                11.057454199999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Black and white",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6bf",
        "timestamp": "2024-08-20T09:13:23.764Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:13:23.764Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948089999999,
                11.0574493
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Dark hull with white stripes",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c0",
        "timestamp": "2024-08-20T09:13:23.764Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:13:23.764Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948089999999,
                11.0574493
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c1",
        "timestamp": "2024-08-20T09:13:53.783Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:13:53.783Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694875,
                11.0574443
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c3",
        "timestamp": "2024-08-20T09:13:53.783Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:13:53.783Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694875,
                11.0574443
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c4",
        "timestamp": "2024-08-20T09:14:23.821Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:14:23.821Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694856,
                11.057442
            ]
        },
        "category": "Cargo Ship",
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c5",
        "timestamp": "2024-08-20T09:14:23.821Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:14:23.821Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694856,
                11.057442
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Gray and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c6",
        "timestamp": "2024-08-20T09:14:23.821Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:14:23.821Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694856,
                11.057442
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c7",
        "timestamp": "2024-08-20T09:14:54.134Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:14:54.134Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948859999999,
                11.0574385
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with black markings",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c8",
        "timestamp": "2024-08-20T09:14:54.134Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:14:54.134Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948859999999,
                11.0574385
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Dark blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0559590a2f21eaa6c9",
        "timestamp": "2024-08-20T09:15:24.143Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:15:24.143Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694825,
                11.057433399999999
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "small",
        "others": "The vessel has a distinctive emblem on the side."
    },
    {
        "_id": "66fa8f0559590a2f21eaa6ca",
        "timestamp": "2024-08-20T09:15:24.143Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:15:24.143Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694825,
                11.057433399999999
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6cb",
        "timestamp": "2024-08-20T09:15:54.174Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:15:54.174Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269486,
                11.0574288
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white",
        "size": "medium",
        "others": "The vessel is a coast guard ship with visible personnel on deck."
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6cc",
        "timestamp": "2024-08-20T09:15:54.174Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:15:54.174Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269486,
                11.0574288
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6cd",
        "timestamp": "2024-08-20T09:16:24.196Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:16:24.196Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948709999999,
                11.0574286
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": "The vessel appears to be a patrol vessel with personnel on board."
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d0",
        "timestamp": "2024-08-20T09:16:54.309Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:16:54.309Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269489,
                11.0574353
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "Medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d1",
        "timestamp": "2024-08-20T09:16:54.309Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:16:54.309Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269489,
                11.0574353
            ]
        },
        "category": "Container Ship",
        "super_category": "Cargo",
        "color": "Dark with white and red accents",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d2",
        "timestamp": "2024-08-20T09:17:24.419Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:17:24.419Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947899999999,
                11.0574491
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d3",
        "timestamp": "2024-08-20T09:17:24.419Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:17:24.419Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947899999999,
                11.0574491
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d4",
        "timestamp": "2024-08-20T09:17:54.461Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:17:54.461Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947469999999,
                11.0574464
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": "The vessel has a visible insignia and appears to be a patrol boat with personnel on deck."
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d5",
        "timestamp": "2024-08-20T09:17:54.461Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:17:54.461Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947469999999,
                11.0574464
            ]
        },
        "category": "Dredger",
        "super_category": "Special Craft",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f0c59590a2f21eaa6d6",
        "timestamp": "2024-08-20T09:17:54.461Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:17:54.461Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947469999999,
                11.0574464
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Blue and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6d7",
        "timestamp": "2024-08-20T09:18:24.551Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:18:24.551Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694758,
                11.057452699999999
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6d8",
        "timestamp": "2024-08-20T09:18:24.551Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:18:24.551Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694758,
                11.057452699999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Dark with white and red accents",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6d9",
        "timestamp": "2024-08-20T09:18:54.654Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:18:54.654Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948139999999,
                11.057442199999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6da",
        "timestamp": "2024-08-20T09:19:24.708Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:19:24.708Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694801,
                11.057448599999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with black and blue markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6db",
        "timestamp": "2024-08-20T09:19:24.708Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:19:24.708Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694801,
                11.057448599999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Dark with white and red accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6dc",
        "timestamp": "2024-08-20T09:19:54.712Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:19:54.712Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947949999999,
                11.057462
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6dd",
        "timestamp": "2024-08-20T09:19:54.712Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:19:54.712Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947949999999,
                11.057462
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1359590a2f21eaa6de",
        "timestamp": "2024-08-20T09:20:24.780Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:20:24.780Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948,
                11.057463
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6df",
        "timestamp": "2024-08-20T09:20:54.875Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:20:54.875Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947829999999,
                11.057457999999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e0",
        "timestamp": "2024-08-20T09:21:24.902Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:21:24.902Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948209999999,
                11.057453899999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e1",
        "timestamp": "2024-08-20T09:21:24.902Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:21:24.902Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948209999999,
                11.057453899999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e2",
        "timestamp": "2024-08-20T09:21:54.994Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:21:54.994Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694784,
                11.0574509
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White with black markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e3",
        "timestamp": "2024-08-20T09:21:54.994Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:21:54.994Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694784,
                11.0574509
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e4",
        "timestamp": "2024-08-20T09:22:25.083Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:22:25.083Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694782,
                11.0574558
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e5",
        "timestamp": "2024-08-20T09:22:25.083Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:22:25.083Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694782,
                11.0574558
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and white with black accents",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e6",
        "timestamp": "2024-08-20T09:22:55.107Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:22:55.107Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948209999999,
                11.057465599999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is a coast guard ship with visible personnel on deck."
    },
    {
        "_id": "66fa8f1759590a2f21eaa6e7",
        "timestamp": "2024-08-20T09:22:55.107Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:22:55.107Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948209999999,
                11.057465599999999
            ]
        },
        "category": "Tug",
        "super_category": "Tugs",
        "color": "Red and black",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6e8",
        "timestamp": "2024-08-20T09:23:25.224Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:23:25.224Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694798,
                11.0574707
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6e9",
        "timestamp": "2024-08-20T09:23:55.308Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:23:55.308Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269481,
                11.057475
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6ea",
        "timestamp": "2024-08-20T09:23:55.308Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:23:55.308Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269481,
                11.057475
            ]
        },
        "category": "Bulk Carrier",
        "super_category": "Cargo",
        "color": "Gray and white",
        "size": "large",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6eb",
        "timestamp": "2024-08-20T09:24:25.359Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:24:25.359Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694789,
                11.057474299999999
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White with black and red markings",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6ec",
        "timestamp": "2024-08-20T09:24:25.359Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:24:25.359Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694789,
                11.057474299999999
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f1d59590a2f21eaa6ed",
        "timestamp": "2024-08-20T09:24:55.428Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:24:55.428Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26947129999999,
                11.057478999999999
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel appears to be a patrol or coast guard vessel."
    },
    {
        "_id": "66fa8f2359590a2f21eaa6f0",
        "timestamp": "2024-08-20T09:28:55.837Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:28:55.837Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694784,
                11.0574622
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "red and white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f2359590a2f21eaa6f1",
        "timestamp": "2024-08-20T09:29:25.907Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:29:25.907Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26948499999999,
                11.057462
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "medium",
        "others": "The vessel has a logo on the side."
    },
    {
        "_id": "66fa8f2359590a2f21eaa6f2",
        "timestamp": "2024-08-20T09:29:55.947Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:29:55.947Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694853,
                11.0574639
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f2359590a2f21eaa6f3",
        "timestamp": "2024-08-20T09:30:25.979Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:30:25.979Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694769,
                11.0574741
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "white",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f2359590a2f21eaa6f4",
        "timestamp": "2024-08-20T09:30:25.979Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:30:25.979Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694769,
                11.0574741
            ]
        },
        "category": null,
        "super_category": "Tugs",
        "color": "Dark with some red and white sections",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f2b59590a2f21eaa6f5",
        "timestamp": "2024-08-20T09:30:56.061Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:30:56.061Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694782,
                11.0574845
            ]
        },
        "category": "Patrol Vessel",
        "super_category": "Military",
        "color": "White with dark accents",
        "size": "medium",
        "others": "The vessel has a visible emblem on the superstructure."
    },
    {
        "_id": "66fa8f2b59590a2f21eaa6f6",
        "timestamp": "2024-08-20T09:31:26.084Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:31:26.084Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694722,
                11.057498899999999
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f2b59590a2f21eaa6f7",
        "timestamp": "2024-08-20T09:31:56.178Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:31:56.178Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26946129999999,
                11.0575029
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "White with black and red markings",
        "size": "medium",
        "others": "The vessel has a distinctive emblem on the side."
    },
    {
        "_id": "66fa8f2b59590a2f21eaa6f8",
        "timestamp": "2024-08-20T09:32:26.230Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:32:26.230Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26945169999999,
                11.0575084
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": "The vessel has a visible identification number and is marked with 'PHILIPPINE'. It appears to be a patrol boat."
    },
    {
        "_id": "66fa8f2b59590a2f21eaa6f9",
        "timestamp": "2024-08-20T09:32:56.278Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:32:56.278Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694453,
                11.0575072
            ]
        },
        "category": "Coast Guard Vessel",
        "super_category": "Special Craft",
        "color": "White",
        "size": "medium",
        "others": "The vessel is part of the Philippine Coast Guard."
    },
    {
        "_id": "66fa8f3659590a2f21eaa6fa",
        "timestamp": "2024-08-20T09:33:26.356Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:33:26.356Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26943539999999,
                11.0575
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "White",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f3659590a2f21eaa6fb",
        "timestamp": "2024-08-20T09:33:56.420Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:33:56.420Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26943449999999,
                11.0574979
            ]
        },
        "category": "Patrol Boat",
        "super_category": "Military",
        "color": "Black and white",
        "size": "medium",
        "others": "The vessel appears to be a coast guard patrol boat."
    },
    {
        "_id": "66fa8f3659590a2f21eaa6fc",
        "timestamp": "2024-08-20T09:34:26.455Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:34:26.455Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269436,
                11.0574975
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "Black and white",
        "size": "medium",
        "others": "The vessel appears to be a coast guard ship with distinctive markings."
    },
    {
        "_id": "66fa8f3659590a2f21eaa6fe",
        "timestamp": "2024-08-20T09:43:27.422Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:43:27.422Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269453,
                11.057454499999999
            ]
        },
        "category": "Tugboat",
        "super_category": "Tugs",
        "color": "Black and white",
        "size": "small",
        "others": null
    },
    {
        "_id": "66fa8f3d59590a2f21eaa6ff",
        "timestamp": "2024-08-20T09:43:57.435Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:43:57.435Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.269449,
                11.0574582
            ]
        },
        "category": null,
        "super_category": "Military",
        "color": "Gray",
        "size": "medium",
        "others": null
    },
    {
        "_id": "66fa8f3d59590a2f21eaa700",
        "timestamp": "2024-08-20T09:44:27.486Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:44:27.486Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.26944859999999,
                11.057468199999999
            ]
        },
        "category": null,
        "super_category": "Special Craft",
        "color": "White with black markings",
        "size": "medium",
        "others": "The vessel appears to be a patrol or service craft with personnel on deck."
    },
    {
        "_id": "66fa8f3d59590a2f21eaa703",
        "timestamp": "2024-08-20T09:46:27.649Z",
        "bucket_name": "smartmast-prototype-32-ap",
        "unit_id": "prototype-32",
        "image_path": "artifacts/2024-08-20T06:50:15.820Z/image/prototype-32_cam-1_2024-08-20T09:46:27.649Z.jpg",
        "location": {
            "type": "Point",
            "coordinates": [
                114.2694377,
                11.057502399999999
            ]
        },
        "category": null,
        "super_category": "Cargo",
        "color": "Red and white",
        "size": "large",
        "others": null
    },
]

module.exports = { artifactsList }