import { Grid, Modal, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import { useNavigate } from "react-router-dom";

export default function DetailModal({ showDetailModal, setShowDetailModal, selectedCard, setSelectedCard, id, signedUrls }) {
    const { screenSize } = useApp();
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const navigate = useNavigate();
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    // const [locationName, setLocationName] = useState('Loading...');
    // const location = selectedCard?.location ? { lat: selectedCard.location.coordinates[1], lng: selectedCard.location.coordinates[0] } : null;
    const key = selectedCard?.location?.coordinates && displayCoordinates(selectedCard.location.coordinates, !!user?.use_MGRS);

    // const fetchGeolocation = async () => {
    //     try {
    //         const name = await getLocation(location);
    //         setLocationName(name);
    //     } catch (err) {
    //         console.error('Error fetching geolocation:', err);
    //     }
    // };

    const handleClose = () => {
        setSelectedCard(null);
        setShowDetailModal(false);
    };

    const handleNavigation = () => {
        if (selectedCard?.location?.coordinates) {
            navigate(`/dashboard/map?artifact=${selectedCard._id}`);
            handleClose();
        }
    };

    const details = [
        { label: "Location", value: key },
        { label: "Category", value: selectedCard?.super_category || "Unspecified category" },
        { label: "Sub Category", value: selectedCard?.category },
        { label: "Weapons", value: selectedCard?.weapons },
        { label: "Size", value: selectedCard?.size },
        { label: "Color", value: selectedCard?.color },
        { label: "Imo Number", value: selectedCard?.imo_number },
        { label: "Country Flag", value: selectedCard?.country_flag },
        { label: "Home Country", value: selectedCard?.home_country },
        { label: "Orientation", value: selectedCard?.vessel_orientation },
        { label: "Features", value: selectedCard?.vessel_features },
        {
            label: "Text Detected",
            value:
                Array.isArray(selectedCard?.text_extraction) && selectedCard.text_extraction.length > 0
                    ? selectedCard.text_extraction
                          .map((e) => e.text)
                          .slice(0, 5)
                          .join(", ")
                    : null,
        },
        { label: "Description", value: selectedCard?.others },
    ];

    const fieldWithFullWidth = ["Text Detected", "Description", "Features"];

    useEffect(() => {
        if (selectedCard && signedUrls) {
            const thumbnailUrl = signedUrls.get(`${selectedCard._id}:thumbnail`);
            const imageUrl = signedUrls.get(`${selectedCard._id}:image`);
            const videoUrl = signedUrls.get(`${selectedCard._id}:video`);

            if (selectedCard.video_path) {
                setSrc(videoUrl || imageUrl || null);
            } else {
                setSrc(imageUrl || thumbnailUrl || null);
            }
        }
    }, [selectedCard, signedUrls]);

    // useEffect(() => {
    //     if (location) {
    //         fetchGeolocation();
    //     }
    // }, [location]);

    return (
        <Modal open={Boolean(showDetailModal)} onClose={handleClose}>
            <ModalContainer title="Event Details" onClose={handleClose} showDivider>
                <Grid container gap={1} maxHeight="70vh" overflow="auto" minWidth={{ xs: 300, sm: 500 }} maxWidth={800}>
                    <Grid size={12}>
                        {selectedCard && (
                            <PreviewMedia
                                thumbnailLink={src}
                                originalLink={src}
                                cardId={id || selectedCard._id}
                                isImage={!selectedCard.video_path}
                                style={{ borderRadius: 8, height: 300, objectFit: "contain", backgroundColor: "#000" }}
                                skeletonStyle={{ height: 300, width: "100%" }}
                                showFullscreenIconForMap={!selectedCard.video_path}
                                showArchiveButton={hasManageArtifacts}
                            />
                        )}
                    </Grid>

                    <Grid
                        display="flex"
                        justifyContent={screenSize.xs ? "flex-start" : "space-between"}
                        alignItems={screenSize.xs ? "flex-start" : "center"}
                        paddingX={1}
                        flexDirection={screenSize.xs ? "column" : "row"}
                        size={12}
                    >
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Name
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {selectedCard?.vesselName}
                        </Typography>
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Timestamp
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {dayjs(selectedCard?.timestamp)
                                // .tz(timezone)
                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>

                    {details.map(({ label, value }, index) => (
                        <React.Fragment key={index}>
                            <Grid
                                display="flex"
                                alignItems={{
                                    xs: "flex-start",
                                    sm: index % 2 == 0 || fieldWithFullWidth.includes(label) ? "flex-start" : "flex-end",
                                }}
                                paddingX={1}
                                flexDirection={"column"}
                                size={{
                                    xs: 12,
                                    sm: fieldWithFullWidth.includes(label) ? 12 : 5.9,
                                }}
                            >
                                <Typography fontSize="16px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                                    {label}
                                </Typography>
                                <Typography
                                    fontSize="16px"
                                    fontWeight={500}
                                    onClick={() => (label === "Location" ? handleNavigation() : null)}
                                    sx={{
                                        cursor: label === "Location" ? "pointer" : "default",
                                        color: label === "Location" ? "#007bff" : "inherit",
                                        textDecoration: label === "Location" ? "underline" : "none",
                                        userSelect: "none",
                                        "&:hover":
                                            label === "Location"
                                                ? {
                                                      color: "#0056b3",
                                                      textDecoration: "underline",
                                                  }
                                                : {},
                                    }}
                                    title={label === "Location" ? "Click to view on map" : ""}
                                >
                                    {value ?? "--"}
                                </Typography>
                            </Grid>
                        </React.Fragment>
                    ))}
                </Grid>
            </ModalContainer>
        </Modal>
    );
}
