import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockIoEmitter = {
    emit: jest.fn()
};

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ArtifactFlag Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ArtifactFlag')];

        const ArtifactFlagModule = await import('../../models/ArtifactFlag');
        const ArtifactFlag = ArtifactFlagModule.default;

        expect(mockDb.qm.model).toHaveBeenCalled();
        expect(ArtifactFlag).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.artifactId).toBeDefined();
        expect(schemaArg.paths.artifactId.type).toBeDefined();
        expect(schemaArg.paths.artifactId.required).toBe(true);
        expect(schemaArg.paths.artifactId.index).toBe(true);

        expect(schemaArg.paths.flaggedBy).toBeDefined();
        expect(schemaArg.paths.flaggedBy.type).toBeDefined();
        expect(schemaArg.paths.flaggedBy.required).toBe(true);

        expect(schemaArg.paths.flaggedAt).toBeDefined();
        expect(schemaArg.paths.flaggedAt.type).toBeDefined();
        expect(schemaArg.paths.flaggedAt.required).toBe(true);
        expect(schemaArg.paths.flaggedAt.default).toBeDefined();

        const flaggedAtDefault = schemaArg.paths.flaggedAt.default();
        expect(typeof flaggedAtDefault).toBe('string');
        expect(flaggedAtDefault).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.index).toHaveBeenCalledTimes(2);
        expect(schemaArg.index).toHaveBeenNthCalledWith(1, { artifactId: 1, flaggedBy: 1 });
        expect(schemaArg.index).toHaveBeenNthCalledWith(2, { flaggedAt: -1 });

        expect(schemaArg.post).toHaveBeenCalledTimes(3);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(3, "deleteMany", expect.any(Function));

        const mockFlag = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', artifactId: 'artifact-id', flaggedBy: 'user-id' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockFlag);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn({ deletedCount: 1 });
            deleteHookFn(mockFlag);
        }

        const deleteManyHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'deleteMany');
        if (deleteManyHookCall) {
            const deleteManyHookFn = deleteManyHookCall[1];
            deleteManyHookFn({ deletedCount: 1 });
            deleteManyHookFn(mockFlag);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});
