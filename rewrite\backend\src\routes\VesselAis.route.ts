import express, { Request, Response, NextFunction } from "express";
import { validateData } from "../middlewares/validator";
import { body, query } from "express-validator";
import { validateError, getUnitIdsFromVessel, canAccessVessel } from "../utils/functions";
import limitPromise from "../modules/pLimit";
import mongoose, { isValidObjectId } from "mongoose";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import db from "../modules/db";
import compression from "compression";
import vesselService from "../services/Vessel.service";
import { IAisLookup, IVesselAis } from "../interfaces/VesselAis";
import { IVessel } from "../interfaces/Vessel";
import { IQueryFilter } from "src/interfaces/Common";

const router = express.Router();

const aisProjection: Record<string, number> = {
    _id: 1,
    location: 1,
    onboard_vessel_id: 1,
    "metadata.message": 1,
    name: 1,
    mmsi: 1,
    timestamp: 1,
};

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

const getRandomDeviation = (): number => {
    return Math.random() * 0.001;
};

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS),
    isAuthenticated,
    (req: Request, res: Response, next: NextFunction) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("startTimestampISO")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestampISO")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselId } = req.params as { vesselId: string };
            const { startTimestampISO, endTimestampISO, lastKnown }: { startTimestampISO?: string; endTimestampISO?: string; lastKnown?: boolean } =
                req.body;
            console.log(`/vesselAis ${vesselId}`, startTimestampISO, endTimestampISO, lastKnown);

            if (!vesselId || !mongoose.Types.ObjectId.isValid(vesselId)) return res.status(400).json({ message: "Invalid vessel ID" });

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
            }

            const vessel: IVessel | null = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
            }

            const historicalUnitIds: string[] = getUnitIdsFromVessel(vessel);
            if (historicalUnitIds.length === 0) return res.status(400).json({ message: "Vessel has no associated unit_id in history" });

            console.log(`/vesselAis ${vesselId} historicalUnitIds`, historicalUnitIds);

            const ts: number = new Date().getTime();

            const collections = historicalUnitIds.map((unitId: string) => db.ais.collection(`${unitId}_ais`));

            // console.log(`/vesselAis ${vesselId} collections`, collections)

            const query: IQueryFilter = { onboard_vessel_id: new mongoose.Types.ObjectId(vesselId) };

            if (startTimestampISO) {
                const endTime: string | number = endTimestampISO || Date.now();
                query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
            }

            console.log(`/vesselAis ${vesselId} query`, query);

            const ais: IVesselAis | IVesselAis[] | null = await limitPromise(async (): Promise<IVesselAis | IVesselAis[] | null> => {
                if (isClosed) {
                    res.end();
                    return null;
                }
                console.log(`/vesselAis ${vesselId} querying DB`);

                if (lastKnown) {
                    const aisPromises = collections.map((collection) =>
                        collection.findOne(
                            { onboard_vessel_id: new mongoose.Types.ObjectId(vesselId) },
                            {
                                projection: aisProjection,
                                sort: { timestamp: -1 },
                            },
                        ),
                    );

                    const allAis = (await Promise.all(aisPromises)).filter(Boolean) as IVesselAis[];

                    if (allAis.length === 0) return null;

                    return allAis.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
                } else {
                    const aisPromises = collections.map((collection) => {
                        const cursor = collection.find(query, {
                            projection: aisProjection,
                        });

                        if (isSwagger) {
                            cursor.limit(20);
                        }

                        return cursor.toArray();
                    });

                    const allAis = await Promise.all(aisPromises);
                    const flattenedResults: IVesselAis[] = allAis
                        .flat()
                        // temporary randomization logic for mock data
                        .map((ais: any) => ({
                            ...ais,
                            location: ais.location && {
                                ...ais.location,
                                coordinates: [ais.location.coordinates[0] + getRandomDeviation(), ais.location.coordinates[1] + getRandomDeviation()],
                            },
                        }));

                    return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                }
            });

            console.log(`/vesselAis ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselAis ${vesselId} received ${(ais && Array.isArray(ais) ? ais.length : 1) || 0} ais`);
            console.log(`/vesselAis ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(ais);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/latest",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS_LATEST),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim()))
            .custom((v: string[]) => v.every((id: string) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO")
            .isISO8601()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        // not supported at the moment
        // query("endTimestamp")
        //     .isInt()
        //     .customSanitizer((v) => Number(v))
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        //     .optional(),
    ]),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselIds, startTimestampISO } = req.query as { vesselIds: string[]; startTimestampISO?: string };

            const ts: number = new Date().getTime();

            const query: IQueryFilter = {};

            if (startTimestampISO) {
                query.last_message_timestamp = { $gte: new Date(startTimestampISO) };
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });
            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };

            console.log(`/vesselAis query`, query);

            const ais: IVesselAis[] = await limitPromise(async (): Promise<IVesselAis[]> => {
                if (isClosed) {
                    res.end();
                    return [];
                }
                const ts: number = new Date().getTime();

                console.log(`/vesselAis querying DB`);

                const cursor = db.lookups.collection<IAisLookup>("ais_mmsi_lookup").find(query);

                if (isSwagger) {
                    cursor.limit(20);
                }

                const ais: IAisLookup[] = await cursor.toArray();

                const lookupCollectionIds: Record<string, string[]> = {};

                ais.forEach((ais) => {
                    if (!lookupCollectionIds[ais.collection]) lookupCollectionIds[ais.collection] = [];
                    lookupCollectionIds[ais.collection].push(ais.last_message_id);
                });

                // console.log(`/vesselAis lookupCollectionIds`, lookupCollectionIds);

                const aisMessages: IVesselAis[] = (
                    await Promise.all(
                        Object.entries(lookupCollectionIds).flatMap(([collection, ids]: [string, string[]]) => {
                            return db.ais
                                .collection<IVesselAis>(collection)
                                .find({ "metadata._id": { $in: ids } }, { projection: aisProjection })
                                .toArray();
                        }),
                    )
                ).flat();

                console.log(`/vesselAis time taken to query ${new Date().getTime() - ts}`);

                // moved this logic to frontend
                // deduplicate aisMessages: keep the latest message for each mmsi
                // const deduplicatedAisMessages = aisMessages
                //     .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                //     .filter((ais, index, self) => index === self.findIndex((t) => t.mmsi === ais.mmsi));

                // console.log('aisMessages', aisMessages);

                return aisMessages;
            });

            const groupedAis = ais.reduce((acc: { [vesselId: string]: IVesselAis[] }, ais: IVesselAis) => {
                const vesselId: string = ais.onboard_vessel_id.toString();
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push(ais);
                return acc;
            }, {});

            vesselIds.forEach((vesselId: string) => {
                if (!groupedAis[vesselId]) {
                    groupedAis[vesselId] = [];
                }
            });

            console.log(`/vesselAis received ${(ais && ais.length) || 0} ais`);
            console.log(`/vesselAis time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(groupedAis);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

export default router;
