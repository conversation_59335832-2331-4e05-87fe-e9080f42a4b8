import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('Permission Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Permission')];

        const PermissionModule = await import('../../models/Permission');
        const Permission = PermissionModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Permission', expect.any(Object));
        expect(Permission).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.permission_id).toBeDefined();
        expect(schemaArg.paths.permission_id.type).toBe(Number);
        expect(schemaArg.paths.permission_id.required).toBe(true);
        expect(schemaArg.paths.permission_id.unique).toBe(true);

        expect(schemaArg.paths.permission_name).toBeDefined();
        expect(schemaArg.paths.permission_name.type).toBe(String);
        expect(schemaArg.paths.permission_name.required).toBe(true);
        expect(schemaArg.paths.permission_name.unique).toBe(true);

        expect(schemaArg.paths.permission_description).toBeDefined();
        expect(schemaArg.paths.permission_description.type).toBe(String);
        expect(schemaArg.paths.permission_description.required).toBe(true);

        expect(schemaArg.paths.assignable).toBeDefined();
        expect(schemaArg.paths.assignable.type).toBe(Boolean);
        expect(schemaArg.paths.assignable.required).toBe(true);
        expect(schemaArg.paths.assignable.default).toBe(true);
    });
});
