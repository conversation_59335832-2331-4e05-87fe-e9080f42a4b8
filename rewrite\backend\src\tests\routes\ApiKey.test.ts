const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { apiEndpointsList } = require('../data/ApiEndpoints');
const ApiEndpoint = require('../../models/ApiEndpoint');
const { apiKeysList } = require('../data/ApiKeys');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('API Keys API', () => {

    describe('GET /api/apiKeys', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/apiKeys');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch the list of API keys if the user is authorized', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.find.mockResolvedValue(apiKeysList);

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        ['_id', 'description', 'allowed_endpoints', 'is_deleted', 'is_revoked', 'api_key', 'requests', 'jwt_token', 'creation_timestamp'].forEach(prop => {
                            expect(res.body[0]).toHaveProperty(prop);
                        });
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 500 if an error occurs while fetching the API keys', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.find.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/apiKeys')
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/apiKeys', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/apiKeys');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', nonAuthToken)
                        .send({ description: 'Test API Key' });

                    expect(res.status).toBe(403);
                });

                it('should return 400 if description is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({});

                    if (authMethod === 'user') {
                        expect(res.status).toBe(400);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 400 if description is an empty string', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: '' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(400);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 200 and create a new API key', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.create.mockResolvedValueOnce({ description: 'Test API Key' });

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: 'Test API Key' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                it('should return 500 if an error occurs during the creation of the API key', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.create.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .post('/api/apiKeys')
                        .set('Authorization', authToken)
                        .send({ description: 'Test API Key' });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('PATCH /api/apiKeys/:id/allowedEndpoints', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = mongoose.Types.ObjectId()

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                        .set('Authorization', nonAuthToken)
                        .send({ allowed_endpoints: [1, 2, 3] });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/allowedEndpoints')
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_endpoints is not an array', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: "not-an-array" });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if allowed_endpoints contains non-integer values', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, "invalid-endpoint", 3] });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockResolvedValueOnce(null); // Simulating non-existing API key

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(404);
                    });

                    it('should return 400 if an invalid endpoint is provided', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockResolvedValueOnce({ _id: '12345', allowed_endpoints: [] });
                        ApiEndpoint.find.mockResolvedValueOnce([{ endpoint_id: 1 }, { endpoint_id: 2 }]); // Simulating available endpoints

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 3] });

                        expect(res.status).toBe(400);
                    });
                }

                it('should return 200 and update the API key with valid allowed_endpoints or 403 for api-key authentication', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.findById.mockResolvedValueOnce({ _id: validObjectId, allowed_endpoints: [], save: jest.fn().mockResolvedValue(true) });
                    ApiEndpoint.find.mockResolvedValueOnce([{ endpoint_id: 1 }, { endpoint_id: 2 }, { endpoint_id: 3 }]); // Simulating available endpoints

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                        .set('Authorization', authToken)
                        .send({ allowed_endpoints: [1, 2, 3] });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the update process', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/allowedEndpoints`)
                            .set('Authorization', authToken)
                            .send({ allowed_endpoints: [1, 2, 3] });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('PATCH /api/apiKeys/:id/revoke', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = '66c4a658077e3ad6e537d418'

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/apiKeys/${validObjectId}/revoke`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/revoke`)
                        .set('Authorization', nonAuthToken)
                        .send({ revoke: true });

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .patch('/api/apiKeys/invalid-id/revoke')
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if revoke is not a boolean', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: 'not-a-boolean' });

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockResolvedValueOnce(null); // Simulating non-existing API key

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        console.log('respoonse in 404 api key error', res)

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and revoke the API key if valid id and revoke status are provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.findById.mockResolvedValueOnce({ _id: validObjectId, is_revoked: false, save: jest.fn().mockResolvedValue(true) }); // Simulating an existing API key

                    const res = await request(app)
                        .patch(`/api/apiKeys/${validObjectId}/revoke`)
                        .set('Authorization', authToken)
                        .send({ revoke: true });

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the revoke process', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockResolvedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .patch(`/api/apiKeys/${validObjectId}/revoke`)
                            .set('Authorization', authToken)
                            .send({ revoke: true });

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('DELETE /api/apiKeys/:id', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = mongoose.Types.ObjectId()

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/apiKeys/${validObjectId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .delete(`/api/apiKeys/${validObjectId}`)
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                if (authMethod === 'user') {
                    it('should return 400 if the id is not a valid ObjectId', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                        const res = await request(app)
                            .delete('/api/apiKeys/invalid-id')
                            .set('Authorization', authToken);

                        expect(res.status).toBe(400);
                    });

                    it('should return 404 if the API key does not exist', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockResolvedValueOnce(null); // Simulating non-existing API key

                        const res = await request(app)
                            .delete(`/api/apiKeys/${validObjectId}`)
                            .set('Authorization', authToken);

                        expect(res.status).toBe(404);
                    });
                }

                it('should return 200 and delete the API key if a valid id is provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiKey.findById.mockResolvedValueOnce({ _id: validObjectId, is_deleted: false, save: jest.fn().mockResolvedValue(true) }); // Simulating an existing API key

                    const res = await request(app)
                        .delete(`/api/apiKeys/${validObjectId}`)
                        .set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs during the delete process', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiKey.findById.mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app)
                            .delete(`/api/apiKeys/${validObjectId}`)
                            .set('Authorization', authToken);

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

});
