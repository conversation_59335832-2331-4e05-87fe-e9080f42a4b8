import { jest } from '@jest/globals';

export const createMockApiKey = (overrides: any = {}) => ({
    _id: '507f1f77bcf86cd799439013',
    name: 'Test API Key',
    key: 'test-api-key',
    is_deleted: false,
    is_revoked: false,
    allowed_endpoints: ['test-endpoint'],
    requests: 0,
    requests_endpoints: {},
    last_used: new Date(),
    organization_id: '507f1f77bcf86cd799439012',
    created_by: '507f1f77bcf86cd799439011',
    creation_timestamp: new Date().toISOString(),
    save: jest.fn().mockResolvedValue({}),
    markModified: jest.fn(),
    toObject: jest.fn().mockReturnValue({ _id: '507f1f77bcf86cd799439013', name: 'Test API Key' }),
    ...overrides
});

export const createMockDeletedApiKey = (overrides: any = {}) => 
    createMockApiKey({ is_deleted: true, ...overrides });

export const createMockRevokedApiKey = (overrides: any = {}) => 
    createMockApiKey({ is_revoked: true, ...overrides });

export const createMockRestrictedApiKey = (allowedEndpoints: string[], overrides: any = {}) => 
    createMockApiKey({ allowed_endpoints: allowedEndpoints, ...overrides });

// ============================================================================
// API KEY MODEL MOCK
// ============================================================================

export const createApiKeyModelMock = () => ({
    find: jest.fn().mockResolvedValue([]),
    findOne: jest.fn().mockResolvedValue(null),
    findById: jest.fn().mockResolvedValue(null),
    findOneAndUpdate: jest.fn().mockResolvedValue(null),
    findOneAndDelete: jest.fn().mockResolvedValue(null),
    create: jest.fn().mockResolvedValue({}),
    updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
    updateMany: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
    deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
    deleteMany: jest.fn().mockResolvedValue({ deletedCount: 1 }),
    aggregate: jest.fn().mockResolvedValue([]),
    countDocuments: jest.fn().mockResolvedValue(0),
    distinct: jest.fn().mockResolvedValue([]),
    save: jest.fn().mockResolvedValue({}),
    markModified: jest.fn(),
    toObject: jest.fn().mockReturnValue({}),
    toJSON: jest.fn().mockReturnValue({}),
    schema: {}
});

// ============================================================================
// API KEY DOCUMENT MOCK
// ============================================================================

export const createMockApiKeyDocument = (data: any = {}) => {
    const apiKeyData = createMockApiKey(data);
    
    return {
        ...apiKeyData,
        save: jest.fn().mockResolvedValue(apiKeyData),
        remove: jest.fn().mockResolvedValue(apiKeyData),
        deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
        markModified: jest.fn(),
        toObject: jest.fn().mockReturnValue(apiKeyData),
        toJSON: jest.fn().mockReturnValue(apiKeyData),
        isModified: jest.fn().mockReturnValue(false),
        isNew: false
    };
};

// ============================================================================
// API KEY SCHEMA MOCK
// ============================================================================

export const createApiKeySchemaMock = () => ({
    pre: jest.fn(),
    post: jest.fn(),
    index: jest.fn(),
    methods: {},
    statics: {},
    paths: {
        name: { type: String, required: true },
        key: { type: String, required: true, unique: true },
        is_deleted: { type: Boolean, default: false },
        is_revoked: { type: Boolean, default: false },
        allowed_endpoints: [{ type: String }],
        requests: { type: Number, default: 0 },
        requests_endpoints: { type: Object, default: {} },
        last_used: { type: Date },
        organization_id: { type: String, required: true },
        created_by: { type: String, required: true },
        creation_timestamp: { 
            type: Date, 
            required: true, 
            default: () => new Date().toISOString() 
        }
    }
});

// ============================================================================
// API KEY TEST UTILITIES
// ============================================================================

export const setupApiKeyTest = () => {
    const mockModel = createApiKeyModelMock();
    const mockApiKey = createMockApiKey();
    const mockDocument = createMockApiKeyDocument();
    
    return {
        mockModel,
        mockApiKey,
        mockDocument
    };
};

export const expectApiKeyCreated = (mockModel: any, apiKeyData: any) => {
    expect(mockModel.create).toHaveBeenCalledWith(apiKeyData);
};

export const expectApiKeyFound = (mockModel: any, query: any) => {
    expect(mockModel.findOne).toHaveBeenCalledWith(query);
};

export const expectApiKeyUpdated = (mockModel: any, query: any, update: any) => {
    expect(mockModel.updateOne).toHaveBeenCalledWith(query, update);
};

export const expectApiKeyDeleted = (mockModel: any, query: any) => {
    expect(mockModel.deleteOne).toHaveBeenCalledWith(query);
};

export const expectApiKeyUsageTracked = (mockApiKey: any, endpoint: string) => {
    expect(mockApiKey.requests).toBeGreaterThan(0);
    expect(mockApiKey.requests_endpoints[endpoint]).toBeGreaterThan(0);
    expect(mockApiKey.last_used).toBeInstanceOf(Date);
    expect(mockApiKey.markModified).toHaveBeenCalledWith('requests_endpoints');
    expect(mockApiKey.save).toHaveBeenCalled();
};

// ============================================================================
// API KEY VALIDATION HELPERS
// ============================================================================

export const createValidApiKeyData = (overrides: any = {}) => ({
    name: 'Test API Key',
    key: 'test-api-key-12345',
    allowed_endpoints: ['users', 'vessels', 'artifacts'],
    organization_id: '507f1f77bcf86cd799439012',
    created_by: '507f1f77bcf86cd799439011',
    ...overrides
});

export const createInvalidApiKeyData = (field: string) => {
    const validData = createValidApiKeyData();
    
    switch (field) {
        case 'name':
            return { ...validData, name: '' };
        case 'key':
            return { ...validData, key: '' };
        case 'allowed_endpoints':
            return { ...validData, allowed_endpoints: [] };
        case 'organization_id':
            return { ...validData, organization_id: '' };
        case 'created_by':
            return { ...validData, created_by: '' };
        default:
            return validData;
    }
};

// ============================================================================
// CLEANUP
// ============================================================================

export const cleanupApiKeyTest = () => {
    jest.clearAllMocks();
};
