import request from 'supertest';
import { describe, it, jest, beforeEach, expect, beforeAll } from "@jest/globals";

jest.mock('../modules/processLogs', () => ({}));
jest.mock('dotenv/config', () => ({}));
jest.mock('../models/SessionLog', () => ({
    create: jest.fn(() => Promise.resolve({ _id: 'test-session-id' })),
    updateOne: jest.fn(() => Promise.resolve({ modifiedCount: 1 }))
}));

const mockIoEmitter = {
    emit: jest.fn(),
    on: jest.fn()
};

jest.mock('../modules/ioEmitter', () => mockIoEmitter);
jest.mock('../modules/swagger', () => ({
    swaggerUi: {
        serve: jest.fn((_req: any, _res: any, next: any) => next()),
        setup: jest.fn(() => (_req: any, res: any) => res.json({ swagger: 'docs' }))
    },
    swaggerDocs: {},
    swaggerConfig: {}
}));
jest.mock('../routes/index', () => {
    const mockRouter = require('express').Router();
    mockRouter.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API'));
    return mockRouter;
});
jest.mock('../routes/v2/index.v2', () => {
    const mockRouter = require('express').Router();
    mockRouter.get('/', (_req: any, res: any) => res.send('Welcome to Quartermaster API v2'));
    return mockRouter;
});

beforeAll(() => {
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.PORT = '5000';
    process.env.NODE_ENV = 'test';
});

beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();
});

describe('Server and routes', () => {
    let app: any;
    let server: any;
    let io: any;

    beforeAll(async () => {
        const serverModule = await import('../server');
        app = serverModule.default;
        server = serverModule.server;
        io = serverModule.io;
    });

    describe('Express API routes', () => {
        it('should return 404 for non-existent routes', async () => {
            const response = await request(app).get('/api/nonexistent');
            expect(response.status).toBe(404);
        });

        it('should serve base API route', async () => {
            const response = await request(app).get('/api');
            expect(response.status).toBe(200);
            expect(response.text).toContain('Welcome to Quartermaster API');
        });

        it('should serve swagger documentation', async () => {
            const response = await request(app).get('/api/docs');
            expect(response.status).toBe(200);
        });

        it('should serve v2 API routes', async () => {
            const response = await request(app).get('/api/v2');
            expect(response.status).toBe(200);
            expect(response.text).toContain('Welcome to Quartermaster API v2');
        });

        it('should handle CORS preflight requests', async () => {
            const response = await request(app)
                .options('/api')
                .set('Origin', 'http://localhost:3000')
                .set('Access-Control-Request-Method', 'GET');
            expect(response.status).toBe(204);
        });

        it('should parse JSON bodies up to 20mb', async () => {
            const largePayload = { data: 'x'.repeat(1000) };
            const response = await request(app)
                .post('/api')
                .send(largePayload);
            expect(response.status).not.toBe(413);
        });

        it('should handle cookies', async () => {
            const response = await request(app)
                .get('/api')
                .set('Cookie', 'test=value');
            expect(response.status).toBe(200);
        });

        it('should set device ID cookie if not present', async () => {
            const response = await request(app).get('/api');
            expect(response.status).toBe(200);
        });
    });

    describe('Server configuration', () => {
        it('should have app defined', () => {
            expect(app).toBeDefined();
        });

        it('should have server defined', () => {
            expect(server).toBeDefined();
        });

        it('should have io defined', () => {
            expect(io).toBeDefined();
        });

        it('should be in test environment', () => {
            expect(process.env.NODE_ENV).toBe('test');
        });

        it('should have JWT_SECRET configured', () => {
            expect(process.env.JWT_SECRET).toBe('test-jwt-secret');
        });
    });

    describe('Process event handlers', () => {
        it('should handle SIGTERM', () => {
            const mockExit = jest.spyOn(process, 'exit').mockImplementation(() => {
                throw new Error('process.exit called');
            });
            
            expect(() => {
                process.emit('SIGTERM');
            }).not.toThrow();
            
            mockExit.mockRestore();
        });

        it('should handle SIGINT', () => {
            const mockExit = jest.spyOn(process, 'exit').mockImplementation(() => {
                throw new Error('process.exit called');
            });
            
            expect(() => {
                process.emit('SIGINT');
            }).not.toThrow();
            
            mockExit.mockRestore();
        });
    });
});
