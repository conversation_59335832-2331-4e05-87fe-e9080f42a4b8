const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const request = require("supertest");
const app = require('../../server');
const utilsFunctions = require('../../utils/functions');
const otpService = require('../../modules/otpService');
const jwt = require('jsonwebtoken');

jest.mock('../../modules/email');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
            findByIdAndUpdate: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Users API', () => {

    describe('GET /api/auth', () => {

        const runTests = (authMethod, header, authValue, userOrApiKey, mockAuthorizationModel) => {
            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                if (authMethod === 'user') {
                    it('should return 401 if auth header is not Basic', async () => {
                        const res = await request(app).get('/api/users/auth').set('Authorization', 'Bearer some-value');
                        expect(res.status).toBe(401);
                    });

                    it('should return 400 if auth header does not contain username or password', async () => {
                        const res = await request(app).get('/api/users/auth').set('Authorization', `Basic ${btoa(`:`)}`);
                        expect(res.status).toBe(400);
                    });

                    it('should return 400 if username is invalid', async () => {
                        User.findOne.mockResolvedValueOnce(null)
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });

                    it('should return 302 status if email_verification_enabled is true and email_verified_device_ids is empty', async () => {
                        const mockUser = {
                            email: '<EMAIL>',
                            email_verified_device_ids: [],
                            email_verification_enabled: true
                        };
                        mockAuthorizationModel.mockResolvedValueOnce(userOrApiKey);
                        User.find.mockResolvedValue(mockUser);
                        User.findOne.mockResolvedValueOnce(mockUser);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(302);
                    });
                }

                if (authMethod === 'api-key') {
                    it('should return 400 if api key has been revoked', async () => {
                        let _userOrApiKey = { ...userOrApiKey }
                        _userOrApiKey.is_revoked = true
                        mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });
                    it('should return 400 if api key has been deleted', async () => {
                        let _userOrApiKey = { ...userOrApiKey }
                        _userOrApiKey.is_deleted = true
                        mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                        const res = await request(app).get('/api/users/auth').set(header, authValue());
                        expect(res.status).toBe(400);
                    });
                }

                it('should return 401 if no auth header is provided', async () => {
                    const res = await request(app).get('/api/users/auth');
                    expect(res.status).toBe(401);
                });

                it('should return 400 or 401 if invalid credentails are provided', async () => {
                    let _userOrApiKey = { ...userOrApiKey }
                    _userOrApiKey.password = 'invalid-password-hash'
                    if (authMethod === 'api-key')
                        _userOrApiKey = null
                    mockAuthorizationModel.mockResolvedValueOnce(_userOrApiKey);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect([400, 401]).toContain(res.status);
                });

                it('should return the jwt token along with expiration', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.save = jest.fn().mockResolvedValue({});
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    ['jwt_token', 'expires'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should not shift tokens if tokens length is less than 10', async () => {
                    const mockUser = JSON.parse(JSON.stringify(userOrApiKey, (key, value) => value));
                    mockUser.email_verification_enabled = false;
                    mockUser.jwt_tokens = ['token1', 'token2', 'token3', 'token4', 'token5', 'token6'];
                    mockUser.save = jest.fn().mockResolvedValue({});
                    mockAuthorizationModel.mockResolvedValueOnce(mockUser);
                    const res = await request(app).get('/api/users/auth').set(header, authValue());
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    ['jwt_token', 'expires'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

                    const res = await request(app).get('/api/users/auth').set(header, authValue());

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', 'Authorization', () => `Basic ${btoa(`test:testtest`)}`, { ...authorizedUser, password: '$2a$10$o1PdnWXbuF6kgxkijsyspedX0EwIra2szVjy4Br/X6qWp1Qcw5C7u' }, User.findOne);
        runTests('api-key', 'qm-api-key', () => 'd21e1a57f2de39b3f4fbd42cf871d9bc', authorizedApiKey, ApiKey.findOne);

    })

    describe('GET /api/users/user', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/users/user');
                    expect(res.status).toBe(401);
                });

                it('should return 404 if token has invalid user_id', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce([null]);
                    const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(404);
                    } else {
                        expect(res.status).toBe(400);
                    }
                });

                it('should return the user object for user authentication, and 400 for api-key authentication', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app).get('/api/users/user').set('Authorization', authToken);
                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(res.body).toBeInstanceOf(Object);
                        ['_id', 'name', 'username', 'role_id', 'deletable', 'is_deleted', 'creation_timestamp', 'role', 'permissions'].forEach(prop => {
                            expect(res.body).toHaveProperty(prop);
                        });
                    } else {
                        expect(res.status).toBe(400);
                    }
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

                    const res = await request(app).get('/api/users/user').set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(500);
                    } else {
                        expect(res.status).toBe(400);
                    }
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);

    })

    describe('GET /api/users', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/users');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if caller does not have required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app).get('/api/users').set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return the users list', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.find.mockResolvedValue(usersList);
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'name', 'username', 'role_id', 'deletable', 'is_deleted', 'creation_timestamp'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.find.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error
                    const res = await request(app).get('/api/users').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);

    })

    describe('POST /api/users', () => {
        beforeEach(() => {
            jest.resetAllMocks();
        });

        it('should return 400 if empty object send in body', async () => {
            const res = await request(app)
                .post('/api/users')
                .send({});
            expect(res.status).toBe(400);
        });

        it('should return 400 if email is not associated with the link', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 1 }));
            User.findOne.mockResolvedValueOnce({ username: 'johndoe' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if role_id is not associated with the link', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 2 }));
            User.findOne.mockResolvedValueOnce({ username: 'johndoe' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if username is already taken', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 1 }));
            User.findOne.mockResolvedValueOnce({ username: 'johndoe' });

            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 400 if email is already taken', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 1 }));
            User.findOne.mockResolvedValueOnce(null).mockResolvedValueOnce({ email: '<EMAIL>' });
            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe1',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });
            expect(res.status).toBe(400);
        });

        it('should return 404 if the role does not exist', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 99 }));
            User.findOne.mockResolvedValue(null); // No existing user
            Role.findOne.mockResolvedValue(null); // Role does not exist
            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 99, // Invalid role
                    token: 'valid-token',
                });
            expect(res.status).toBe(404);
        });

        it('should return 400 due to bad request body', async () => {
            const res = await request(app)
                .post('/api/users')
                .send({
                    name: '',
                    username: '',
                    email: 'john',
                    password: '',
                    role_id: '1',
                    token: null,
                });
            expect(res.status).toBe(400);
        });

        it('should create a user and return success message', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 1 }));
            User.findOne.mockResolvedValueOnce(null); // Username not taken
            User.findOne.mockResolvedValueOnce(null); // Email not taken
            Role.findOne.mockResolvedValueOnce({ role_id: 1 }); // Valid role
            User.create.mockResolvedValueOnce({}); // Simulate user creation
            const res = await request(app)
                .post('/api/users')
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });
            expect(res.status).toBe(201);
        });

        it('should handle internal server errors', async () => {
            jest.spyOn(jwt, 'verify').mockImplementation(() => ({ email: '<EMAIL>', role_id: 1 }));
            User.findOne.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

            const res = await request(app)
                .post(`/api/users`)
                .send({
                    name: 'John Doe',
                    username: 'johndoe',
                    email: '<EMAIL>',
                    password: 'password123',
                    role_id: 1,
                    token: 'valid-token',
                });

            expect(res.status).toBe(500);
        });
    });

    describe('PATCH /api/users/:id/role', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId(); // Simulated valid user ID
                const roleId = 1; // Valid role ID
                const invalidUserId = 'invalidUserId';
                const invalidRoleId = 'invalidRoleId';

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch(`/api/users/${userId}/role`).send({ role_id: roleId });
                    expect(res.status).toBe(401);
                });

                it('should return 403 if caller does not have required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', nonAuthToken)
                        .send({ role_id: roleId });
                    expect(res.status).toBe(403);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch(`/api/users/${invalidUserId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if invalid role id is provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: invalidRoleId });
                    expect(res.status).toBe(400);
                });

                it('should return 404 if the user does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValueOnce(null); // No user found

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });

                    expect(res.status).toBe(404);
                });

                it('should return 404 if the role does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValueOnce({ _id: userId, role_id: 1, save: jest.fn().mockResolvedValue({}) }); // User exists
                    Role.findOne.mockResolvedValue(null); // Role does not exist
                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 2 });

                    expect(res.status).toBe(404);
                });

                if (authMethod === 'user') {
                    it('should return 403 if the user\'s role is forbidden to be edited', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        User.findOne.mockResolvedValueOnce({ _id: userId, role_id: 1, save: jest.fn().mockResolvedValue({}) }); // User exists
                        Role.findOne.mockResolvedValueOnce({ role_id: 2 }); // Role exists

                        const res = await request(app)
                            .patch(`/api/users/${userId}/role`)
                            .set('Authorization', authToken)
                            .send({ role_id: 2 });

                        expect(res.status).toBe(403);
                    });
                }

                it('should update the user role and return a success message', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValueOnce({ _id: userId, role_id: 2, save: jest.fn().mockResolvedValue({}) }); // User exists
                    Role.findOne.mockResolvedValueOnce({ role_id: 3 }); // Role exists

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(200);
                });

                it('should update the user role and return a success message', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValueOnce({ _id: userId, role_id: 2, save: jest.fn().mockResolvedValue({}) }); // User exists
                    Role.findOne.mockResolvedValueOnce({ role_id: 3 }); // Role exists

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: 3 });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

                    const res = await request(app)
                        .patch(`/api/users/${userId}/role`)
                        .set('Authorization', authToken)
                        .send({ role_id: roleId });

                    expect(res.status).toBe(500);
                });
            });

        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('DELETE /api/users/:id', () => {
        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId(); // Simulated valid user ID
                const invalidUserId = 'invalidUserId';

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/users/${userId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if caller does not have required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);
                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', nonAuthToken);
                    expect(res.status).toBe(403);
                });

                it('should return 400 if invalid user id is provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .delete(`/api/users/${invalidUserId}`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if the user does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockResolvedValueOnce(null); // No user found

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should return 400 if the user cannot be deleted', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockResolvedValueOnce({ _id: userId, deletable: false }); // User exists but not deletable

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should delete the user and return a success message', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockResolvedValueOnce({
                        _id: userId,
                        deletable: true,
                        save: jest.fn().mockResolvedValue({}),
                    }); // User exists and can be deleted

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

                    const res = await request(app)
                        .delete(`/api/users/${userId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/users/forgot-password', () => {

        const validEmail = '<EMAIL>'; // Simulated valid email
        const invalidEmail = 'invalid-email'; // Simulated invalid email

        beforeEach(() => {
            jest.resetAllMocks();
        });

        it('should return 400 if email is not provided', async () => {
            const res = await request(app).post('/api/users/forgot-password').send({});
            expect(res.status).toBe(400);
        });

        it('should return 400 if an invalid email format is provided', async () => {
            const res = await request(app).post('/api/users/forgot-password').send({ email: invalidEmail });
            expect(res.status).toBe(400);
        });

        it('should return 404 if no user is found with the provided email', async () => {
            User.findOne.mockResolvedValueOnce(null); // Simulate no user found

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(404);
        });

        it('should return 400 if a reset token already exists and is still valid', async () => {
            User.findOne.mockResolvedValueOnce({
                email: validEmail,
                reset_password_token: 'someToken',
                reset_password_expire: Date.now() + 5 * 60 * 1000 // Token still valid
            });

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(400);
        });

        it('should generate a reset token and send an email', async () => {
            const resetToken = 'generatedResetToken'; // Simulated reset token
            const user = {
                email: validEmail,
                reset_password_token: null,
                reset_password_expire: null,
                save: jest.fn().mockResolvedValue({})
            };

            User.findOne.mockResolvedValueOnce(user); // Simulate user found
            jest.spyOn(crypto, 'randomBytes').mockReturnValueOnce(Buffer.from(resetToken, 'hex')); // Mock crypto function
            jest.spyOn(crypto, 'createHash').mockReturnValueOnce({
                update: jest.fn().mockReturnValueOnce({ digest: jest.fn().mockReturnValueOnce('hashedToken') })
            });
            sendEmail.mockImplementation(jest.fn().mockResolvedValue('Email sent'));

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });

            expect(res.status).toBe(200);
            expect(user.reset_password_token).toBe('hashedToken');
            expect(user.reset_password_expire).toBeGreaterThan(Date.now());
        });

        it('should handle internal server errors', async () => {
            User.findOne.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error

            const res = await request(app).post('/api/users/forgot-password').send({ email: validEmail });
            expect(res.status).toBe(500);
        });
    });

    describe('POST /api/users/reset-password/:token', () => {
        const mockUser = {
            reset_password_token: 'hashed_token', // Example token
            reset_password_expire: Date.now() + 10000, // Token not expired
        };
        const token = 'ea2b081d1a2dce127ca87543c656a062f51504c9fa9f9a353ba8d9122e8a3ca7'; // The token used for the test
        const validPassword = 'validpassword';
        const shortPassword = 'short';

        beforeEach(() => {
            jest.resetAllMocks(); // Reset mocks before each test
        });

        it('should return 400 if invalid request body is provided', async () => {
            const res = await request(app)
                .post(`/api/users/reset-password/invalidToken`)
                .send({ password: '' });

            expect(res.status).toBe(400);
        });

        it('should return 400 if the token is invalid or expired', async () => {
            User.findOne.mockResolvedValueOnce(null); // Simulate no user found with the token

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Invalid or expired token');
        });

        it('should return 400 if the password is less than 8 characters', async () => {
            User.findOne.mockResolvedValueOnce(mockUser); // Simulate user found

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: shortPassword });

            expect(res.status).toBe(400);
        });

        it('should reset the password successfully', async () => {
            // Mock user object and expected behavior
            User.findOne.mockResolvedValueOnce(mockUser);
            mockUser.save = jest.fn().mockResolvedValue({}); // Mock save function

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(200);
            expect(res.body.message).toBe('Password has been reset successfully');
            expect(mockUser.password).toBeDefined(); // Check that password was set
            expect(mockUser.reset_password_token).toBeNull(); // Check token is cleared
            expect(mockUser.reset_password_expire).toBeNull(); // Check expiration is cleared
        });

        it('should handle internal server errors', async () => {
            User.findOne.mockRejectedValueOnce(new Error('Database error')); // Simulate database error

            const res = await request(app)
                .post(`/api/users/reset-password/${token}`)
                .send({ password: validPassword });

            expect(res.status).toBe(500);
        });
    });

    describe('PATCH /api/users/userEmailVerification', () => {
        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                const userId = new mongoose.Types.ObjectId();
                const invalidUserId = 'invalidUserId';

                beforeEach(() => {
                    jest.resetAllMocks();
                });


                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/users/userEmailVerification');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if email_verification_enabled is not a boolean', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'invalid-value' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value");
                });

                it('should return 400 if email_verification_enabled is empty string', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: '' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value");
                });

                it('should return 404 if user is not found', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockResolvedValueOnce(null);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('User does not exist');
                });

                it('should return 200 and update the email_verification_enabled status successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const mockUpdatedUser = {
                        _id: 'user-id',
                        email: '<EMAIL>',
                        email_verification_enabled: true,
                        save: jest.fn()
                    };

                    User.findById.mockResolvedValueOnce(mockUpdatedUser);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('User email verification status updated successfully');
                });

                it('should return 400 and if email_verification_enabled is true and user dont have an email', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const mockUpdatedUser = {
                        _id: 'user-id',
                        email: '',
                        email_verification_enabled: true,
                        save: jest.fn()
                    };

                    User.findById.mockResolvedValueOnce(mockUpdatedUser);

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Email is required');
                });

                it('should return 500 if an error occurs during the update', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findById.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .patch('/api/users/userEmailVerification')
                        .set('Authorization', authToken)
                        .query({ email_verification_enabled: 'true' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
    });

    describe('POST /api/users/emailOTPVerification', () => {
        beforeEach(() => {
            jest.resetAllMocks();
        });

        it('should return 401 if no token is provided', async () => {
            const res = await request(app).post('/api/users/emailOTPVerification').send({ username: 'testuser', otp: 123456 });
            expect(res.status).toBe(400);
        });

        it('should return 400 if username is not provided', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Username is required');
        });

        it('should return 400 if username is empty string', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: '', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Invalid value '' provided for field 'username'");
        });

        it('should return 400 if otp is empty string', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: null });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Field 'otp' cannot be empty");
        });

        it('should return 400 if otp is not a valid 6-digit integer', async () => {
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 'invalid' });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe("Field 'otp' must be a 6-digit integer");
        });

        it('should return 400 if user is not found', async () => {
            User.findOne.mockResolvedValueOnce(null);
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'nonexistentuser', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Invalid credentials');
        });

        it('should return 400 if email is not available for the user', async () => {
            const mockUser = { username: 'testuser', email: null };
            User.findOne.mockResolvedValueOnce(mockUser);
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 123456 });
            expect(res.status).toBe(400);
            expect(res.body.message).toBe('Email is required');
        });

        it('should return 400 if OTP is invalid', async () => {
            const mockUser = { email: '<EMAIL>' };
            User.findOne.mockResolvedValueOnce(mockUser);
            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .send({ username: 'testuser', otp: 654321 });
            expect(res.status).toBe(400);
        });

        it('should return 200 if OTP is verified successfully', async () => {
            const mockUser = {
                email: '<EMAIL>',
                email_verified_device_ids: [],
                save: jest.fn()
            };
            otpService.otpStore.push({
                email: mockUser.email,
                otp: 123456,
                expiresAt: new Date() + (1000 * 60)
            });

            User.findOne.mockResolvedValueOnce(mockUser);

            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .set('Cookie', 'deviceId=device12345')
                .send({ username: 'testuser', otp: 123456 });

            expect(res.status).toBe(200);
            expect(mockUser.save).toHaveBeenCalled();
        });

        it('should not update or save user if deviceId already exists', async () => {
            const mockUser = {
                email: '<EMAIL>',
                email_verified_device_ids: ['device12345'],
                save: jest.fn()
            };
            otpService.otpStore.push({
                email: mockUser.email,
                otp: 123456,
                expiresAt: new Date() + (1000 * 60)
            });

            User.findOne.mockResolvedValueOnce(mockUser);

            const res = await request(app)
                .post('/api/users/emailOTPVerification')
                .set('Cookie', 'deviceId=device12345')
                .send({ username: 'testuser', otp: 123456 });

            expect(res.status).toBe(200);
            expect(mockUser.save).not.toHaveBeenCalled();
        });
    });

    describe('POST /api/users/sendEmailOTP', () => {
        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 400 if username is not provided', async () => {
                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Username is required');
                });

                it('should return 400 if username is empty string', async () => {
                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: '' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value '' provided for field 'username'");
                });

                it('should return 400 if user with provided username is not found', async () => {
                    const mockUser = null;
                    User.findOne.mockResolvedValueOnce(mockUser);

                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'nonexistentuser' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Invalid credentials');
                });

                it('should return 400 if user does not have an email', async () => {
                    const mockUser = { username: 'testuser', email: '' };
                    User.findOne.mockResolvedValueOnce(mockUser);

                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Email is required');
                });

                it('should return 200 and send OTP successfully if email is found', async () => {
                    const mockUser = { username: 'testuser', email: '<EMAIL>' };
                    User.findOne.mockResolvedValueOnce(mockUser);

                    const mockSendOtpResponse = { message: 'OTP sent successfully' };
                    jest.spyOn(otpService, 'sendOtp').mockResolvedValueOnce(mockSendOtpResponse);


                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockSendOtpResponse);
                });

                it('should return 500 and throw error', async () => {
                    const mockUser = { username: 'testuser', email: '<EMAIL>' };
                    User.findOne.mockResolvedValueOnce(mockUser);

                    const mockError = new Error('Something went wrong');

                    jest.spyOn(otpService, 'sendOtp');
                    sendEmail.mockImplementation(jest.fn().mockRejectedValueOnce(mockError));


                    const res = await request(app)
                        .post('/api/users/sendEmailOTP')
                        .set('Authorization', authToken)
                        .send({ username: 'testuser' });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/users/invite', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/users/invite');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if role_id is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'undefined' provided for field 'role_id'");
                });

                it('should return 400 if email is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: 'invalidEmail' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("Invalid value 'invalidEmail' provided for field 'email'");
                });


                it('should return 400 if role_id is not an integer', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 'not-a-number' });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toContain('Invalid value');
                });

                it('should return 400 if user with the given email already exists', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValue({ _id: mongoose.Types.ObjectId(), email: '<EMAIL>' });

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('User is already exist with this email');
                });

                it('should return 400 if role does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValue(null);
                    Role.findOne.mockResolvedValue(null);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 999 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('Role is not exist with this id');
                });

                it('should successfully send an invitation email', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Role.findOne.mockResolvedValue({ role_id: 1, role_name: 'Admin' });
                    User.findOne.mockResolvedValueOnce(null);
                    const mockLink = 'http://example.com/invite-link';
                    jest.spyOn(utilsFunctions, 'generateInvitationLink').mockResolvedValue(mockLink);
                    sendEmail.mockResolvedValue(true);

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1 });

                    expect(res.status).toBe(200);
                    expect(sendEmail).toHaveBeenCalled();
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockRejectedValueOnce(new Error('Something went wrong'));

                    const res = await request(app)
                        .post('/api/users/invite')
                        .set('Authorization', authToken)
                        .send({ email: '<EMAIL>', role_id: 1 });

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/users/verify-invite', () => {
        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 400 if invite token is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .set('Authorization', authToken)
                    expect(res.status).toBe(400);
                });

                it('should return 302 and redirect', async () => {
                    jest.spyOn(jwt, 'verify').mockImplementation(() => 'token');
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    User.findOne.mockResolvedValueOnce(null);
                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .set('Authorization', authToken)
                        .query({ token: 'valid-token' });
                    expect(res.status).toBe(302);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    jest.spyOn(jwt, 'verify').mockImplementation(() => {
                        throw new Error('Internal server error');
                    });

                    const res = await request(app)
                        .get('/api/users/verify-invite')
                        .query({ token: 'valid-token' });

                    expect(res.status).toBe(500);
                });

            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
