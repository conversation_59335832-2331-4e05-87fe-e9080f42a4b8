import jwt from 'jsonwebtoken'
import mongoose from 'mongoose';
import { IAuthUser } from 'src/interfaces/User';

const generateUserToken = (user_id: string) => {
    const token = jwt.sign({ user_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
    authorizedUser.jwt_tokens.push(token);
    nonAuthorizedUser['jwt_tokens'].push(token);
    return token;
}

const generateApiToken = (api_key_id: string) => {
    return jwt.sign({ api_key_id }, process.env.JWT_SECRET as string, { expiresIn: '24h' });
}

const authorizedUser: IAuthUser & { save: Function } = {
    _id: '66e2e452bca74bbdb726369c',
    name: 'test-admin',
    username: 'test',
    role_id: 1,
    deletable: false,
    creation_timestamp: new Date('2024-09-12T12:53:38.665Z'),
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
    organization_id: '66e2e452bca74bbdb726369c',
    role: {
        _id: '66cf1154ee65876e64371c9d',
        role_id: 1,
        role_name: 'Admin',
        hierarchy_number: 1,
        denied_permissions: [],
        deletable: false,
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z'),
        editable: false,
    },
    permissions: [
        {
            _id: '66d6fd7de480fbb5a5e1abc6',
            permission_id: 100,
            permission_name: 'MANAGE_ROLES',
            permission_description: 'User can add, remove, and update roles',
            assignable: true,
        },
        {
            _id: '66d70185e480fbb5a5e1abc9',
            permission_id: 200,
            permission_name: 'MANAGE_USERS',
            permission_description: 'User can update roles for the users',
            assignable: true,
        },
        {
            _id: '66d701b0e480fbb5a5e1abcb',
            permission_id: 300,
            permission_name: 'CHANGE_REGION',
            permission_description: 'User can switch region on the dashboard',
            assignable: true,
        },
        {
            _id: '66e0546871fd26bbd48668a2',
            permission_id: 400,
            permission_name: 'VIEW_SESSION_LOGS',
            permission_description: 'User view session logs on the dashboard',
            assignable: true,
        },
        {
            _id: '66f2ab205fc1d8f82af7c720',
            permission_id: 500,
            permission_name: 'MANAGE_API_KEYS',
            permission_description: 'User can manage API developer keys on the dashboard',
            assignable: false,
        },
        {
            _id: "6707dd36af2c1b345bd06b8f",
            permission_id: 600,
            permission_name: "VIEW_STATISTICS",
            permission_description: "User can view statistics on the dashboard",
            assignable: true
        }
    ],
    organization: {
        _id: '66e2e452bca74bbdb726369c',
        name: 'Test Organization',
        domain: 'test.com',
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId('66e2e452bca74bbdb726369c'),
        creation_timestamp: new Date('2024-09-03T18:24:59.698Z')
    },
    save: jest.fn().mockResolvedValue({})
};

const nonAuthorizedUser: IAuthUser & { save: Function } = {
    _id: "66f4288532250d3bfa2e64bc",
    name: "test-user",
    username: "testuser",
    role_id: 2,
    deletable: true,
    is_deleted: false,
    email_verification_enabled: true,
    email_verified_device_ids: [],
    jwt_tokens: [],
    creation_timestamp: new Date("2024-09-25T15:13:09.826Z"),
    allowed_vessels: [],
    created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
    organization_id: "66e2e452bca74bbdb726369c",
    role: {
        _id: "66cf1178ee65876e64371c9e",
        role_id: 2,
        role_name: "User",
        hierarchy_number: 1,
        denied_permissions: [
            100,
            200,
            300,
            400,
            500,
            600
        ],
        deletable: false,
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z"),
        editable: false
    },
    permissions: [],
    organization: {
        _id: "66e2e452bca74bbdb726369c",
        name: "Test Organization",
        domain: "test.com",
        is_internal: true,
        is_miscellaneous: false,
        created_by: new mongoose.Types.ObjectId("66e2e452bca74bbdb726369c"),
        creation_timestamp: new Date("2024-09-03T18:24:59.698Z")
    },
    save: jest.fn().mockResolvedValue({})
}

const authorizedApiKey = {
    "_id": "66f2c43345fbceb6fc036b34",
    "description": "This is a test key",
    "allowed_endpoints": [
        101,
        102,
        103,
        104,
        105,
        106,
        107,
        108,
        109,
        110,
        201,
        202,
        301,
        401,
        501,
        502,
        503,
        601,
        602,
        603,
        604,
        605,
        701,
        801,
        802,
        901,
        1001,
        1101,
        1201,
        1202,
        1301
    ],
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "d21e1a57f2de39b3f4fbd42cf871d9bc",
    "__v": 18,
    "requests": 140,
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
    "creation_timestamp": {
        "$date": "2024-09-24T13:52:51.178Z"
    },
    "save": jest.fn().mockResolvedValue({})
}

const nonAuthorizedApiKey = {
    "_id": "66f2c6e945fbceb6fc036b90",
    "description": "Test key 2",
    "allowed_endpoints": [],
    "is_deleted": false,
    "is_revoked": false,
    "api_key": "079fc9e755a8245654c1c768787ee24c",
    "__v": 15,
    "requests": 0,
    "jwt_token": null,
    "creation_timestamp": {
        "$date": "2024-09-24T14:04:25.153Z"
    },
    "save": jest.fn().mockResolvedValue({})
}

export {
    generateUserToken,
    generateApiToken,
    authorizedUser,
    nonAuthorizedUser,
    authorizedApiKey,
    nonAuthorizedApiKey,
}