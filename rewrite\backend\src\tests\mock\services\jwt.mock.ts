/**
 * JWT Service Mocks
 * 
 * Professional mocks for JWT authentication service
 */

import { jest } from '@jest/globals';

// ============================================================================
// JWT LIBRARY MOCK
// ============================================================================

export const createJWTMock = () => ({
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    decode: jest.fn().mockReturnValue({ user_id: 'test-user-id', iat: Date.now() }),
    
    // JWT Error classes
    JsonWebTokenError: class JsonWebTokenError extends Error {
        constructor(message: string) {
            super(message);
            this.name = 'JsonWebTokenError';
        }
    },
    
    TokenExpiredError: class TokenExpiredError extends Error {
        constructor(message: string, expiredAt: Date) {
            super(message);
            this.name = 'TokenExpiredError';
            this.expiredAt = expiredAt;
        }
    },
    
    NotBeforeError: class NotBeforeError extends Error {
        constructor(message: string, date: Date) {
            super(message);
            this.name = 'NotBeforeError';
            this.date = date;
        }
    }
});

// ============================================================================
// JWT SERVICE SCENARIOS
// ============================================================================

export const setupJWTScenarios = () => {
    const mockJWT = createJWTMock();
    
    return {
        mockJWT,
        
        // Scenario: Valid JWT token
        validToken: (payload: any = { user_id: 'test-user-id' }) => {
            mockJWT.verify.mockReturnValue(payload);
            mockJWT.decode.mockReturnValue({ ...payload, iat: Date.now() });
            return payload;
        },
        
        // Scenario: Valid API key token
        validApiKeyToken: (payload: any = { api_key_id: 'test-api-key-id' }) => {
            mockJWT.verify.mockReturnValue(payload);
            mockJWT.decode.mockReturnValue({ ...payload, iat: Date.now() });
            return payload;
        },
        
        // Scenario: Invalid token format
        invalidToken: (errorMessage = 'Invalid token') => {
            const error = new mockJWT.JsonWebTokenError(errorMessage);
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        },
        
        // Scenario: Expired token
        expiredToken: (errorMessage = 'Token expired') => {
            const error = new mockJWT.TokenExpiredError(errorMessage, new Date());
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        },
        
        // Scenario: Token not active yet
        notBeforeToken: (errorMessage = 'Token not active') => {
            const error = new mockJWT.NotBeforeError(errorMessage, new Date());
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        },
        
        // Scenario: Malformed token
        malformedToken: () => {
            const error = new mockJWT.JsonWebTokenError('Malformed token');
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        },
        
        // Scenario: Generic error
        genericError: (errorMessage = 'JWT processing error') => {
            const error = new Error(errorMessage);
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        },
        
        // Scenario: Unknown error type
        unknownError: (errorValue: any = 'Unknown error') => {
            mockJWT.verify.mockImplementation(() => { throw errorValue; });
            return errorValue;
        },
        
        // Scenario: Unexpected JWT payload
        unexpectedPayload: (payload: any = { unexpected_key: 'value' }) => {
            mockJWT.verify.mockReturnValue(payload);
            return payload;
        },
        
        // Scenario: Missing secret
        missingSecret: () => {
            const error = new mockJWT.JsonWebTokenError('Secret key required');
            mockJWT.verify.mockImplementation(() => { throw error; });
            return error;
        }
    };
};

// ============================================================================
// JWT TOKEN GENERATION HELPERS
// ============================================================================

export const createJWTTokenHelpers = () => ({
    // Generate user token
    generateUserToken: (userId: string = 'test-user-id', expiresIn: string = '1h') => {
        const payload = { user_id: userId };
        const token = `user-token-${userId}-${Date.now()}`;
        return { payload, token };
    },
    
    // Generate API key token
    generateApiKeyToken: (apiKeyId: string = 'test-api-key-id', expiresIn: string = '30d') => {
        const payload = { api_key_id: apiKeyId };
        const token = `api-key-token-${apiKeyId}-${Date.now()}`;
        return { payload, token };
    },
    
    // Generate refresh token
    generateRefreshToken: (userId: string = 'test-user-id') => {
        const payload = { user_id: userId, type: 'refresh' };
        const token = `refresh-token-${userId}-${Date.now()}`;
        return { payload, token };
    },
    
    // Generate password reset token
    generatePasswordResetToken: (userId: string = 'test-user-id') => {
        const payload = { user_id: userId, type: 'password_reset' };
        const token = `reset-token-${userId}-${Date.now()}`;
        return { payload, token };
    },
    
    // Generate email verification token
    generateEmailVerificationToken: (userId: string = 'test-user-id') => {
        const payload = { user_id: userId, type: 'email_verification' };
        const token = `verify-token-${userId}-${Date.now()}`;
        return { payload, token };
    }
});

// ============================================================================
// JWT VALIDATION HELPERS
// ============================================================================

export const createJWTValidationHelpers = () => ({
    // Validate token format
    isValidTokenFormat: (token: string) => {
        return token && typeof token === 'string' && token.split('.').length === 3;
    },
    
    // Extract bearer token
    extractBearerToken: (authHeader: string) => {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        return authHeader.substring(7);
    },
    
    // Check if token is expired
    isTokenExpired: (decodedToken: any) => {
        if (!decodedToken.exp) return false;
        return Date.now() >= decodedToken.exp * 1000;
    },
    
    // Check if token is active
    isTokenActive: (decodedToken: any) => {
        if (!decodedToken.nbf) return true;
        return Date.now() >= decodedToken.nbf * 1000;
    },
    
    // Validate token payload
    validateTokenPayload: (payload: any, requiredFields: string[] = []) => {
        if (!payload || typeof payload !== 'object') return false;
        return requiredFields.every(field => payload.hasOwnProperty(field));
    }
});

// ============================================================================
// JWT TEST UTILITIES
// ============================================================================

export const setupJWTTest = () => {
    const mockJWT = createJWTMock();
    const scenarios = setupJWTScenarios();
    const tokenHelpers = createJWTTokenHelpers();
    const validationHelpers = createJWTValidationHelpers();
    
    return {
        mockJWT,
        scenarios,
        tokenHelpers,
        validationHelpers
    };
};

export const expectTokenSigned = (mockJWT: any, payload: any, secret: string, options?: any) => {
    if (options) {
        expect(mockJWT.sign).toHaveBeenCalledWith(payload, secret, options);
    } else {
        expect(mockJWT.sign).toHaveBeenCalledWith(payload, secret);
    }
};

export const expectTokenVerified = (mockJWT: any, token: string, secret: string, options?: any) => {
    if (options) {
        expect(mockJWT.verify).toHaveBeenCalledWith(token, secret, options);
    } else {
        expect(mockJWT.verify).toHaveBeenCalledWith(token, secret);
    }
};

export const expectTokenDecoded = (mockJWT: any, token: string, options?: any) => {
    if (options) {
        expect(mockJWT.decode).toHaveBeenCalledWith(token, options);
    } else {
        expect(mockJWT.decode).toHaveBeenCalledWith(token);
    }
};

// ============================================================================
// JWT ERROR TESTING HELPERS
// ============================================================================

export const expectJWTError = (mockJWT: any, errorType: string, errorMessage?: string) => {
    switch (errorType) {
        case 'JsonWebTokenError':
            expect(() => mockJWT.verify()).toThrow(mockJWT.JsonWebTokenError);
            break;
        case 'TokenExpiredError':
            expect(() => mockJWT.verify()).toThrow(mockJWT.TokenExpiredError);
            break;
        case 'NotBeforeError':
            expect(() => mockJWT.verify()).toThrow(mockJWT.NotBeforeError);
            break;
        default:
            expect(() => mockJWT.verify()).toThrow();
    }
};

// ============================================================================
// CLEANUP
// ============================================================================

export const cleanupJWTTest = () => {
    jest.clearAllMocks();
};
