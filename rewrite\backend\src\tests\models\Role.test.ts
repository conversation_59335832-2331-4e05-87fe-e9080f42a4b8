import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockIoEmitter = {
    emit: jest.fn()
};

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    if (definition && typeof definition === 'object') {
        Object.keys(definition).forEach(key => {
            this.paths[key] = definition[key];
        });
    }

    this.index = jest.fn();
    this.pre = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._pres.has(event)) {
            this.s.hooks._pres.set(event, []);
        }
        this.s.hooks._pres.get(event).push({ fn });
    });
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

const mockPermission = {
    find: jest.fn().mockResolvedValue([
        { permission_id: 1 },
        { permission_id: 2 },
        { permission_id: 3 }
    ])
};

describe('Role Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);
        jest.doMock('../../models/Permission', () => ({ default: mockPermission }));

        delete require.cache[require.resolve('../../models/Role')];

        const RoleModule = await import('../../models/Role');
        const Role = RoleModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Role', expect.any(Object));
        expect(Role).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const preHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preHookCall) {
            const preHookFn = preHookCall[1];
            
            const mockRole1 = {
                isNew: true,
                constructor: jest.fn().mockReturnValue({
                    findOne: jest.fn().mockReturnValue({
                        sort: jest.fn().mockResolvedValue({ role_id: 5 })
                    })
                }),
                role_id: null,
                hierarchy_number: null,
                denied_permissions: null
            };
            const next1 = jest.fn();
            await preHookFn.call(mockRole1, next1);

            const mockRole2 = {
                isNew: false
            };
            const next2 = jest.fn();
            await preHookFn.call(mockRole2, next2);
        }

        expect(schemaArg.pre).toHaveBeenCalledWith("save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenCalledTimes(2);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));

        const mockRole = {
            toObject: jest.fn().mockReturnValue({ _id: 'test-id', role_name: 'Test Role' })
        };

        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const saveHookFn = saveHookCall[1];
            saveHookFn(mockRole);
        }

        const deleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (deleteHookCall) {
            const deleteHookFn = deleteHookCall[1];
            deleteHookFn(mockRole);
        }

        expect(mockIoEmitter.emit).toHaveBeenCalled();
    });
});