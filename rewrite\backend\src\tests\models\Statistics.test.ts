import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; },
    Mixed: function() { return 'mock-mixed'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; },
        Mixed: function() { return 'mock-mixed'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('Statistics Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Statistics')];

        const StatisticsModule = await import('../../models/Statistics');
        const Statistics = StatisticsModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Statistics', expect.any(Object));
        expect(Statistics).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.stats).toBeDefined();
        expect(schemaArg.paths.stats.type).toBeDefined();
        expect(schemaArg.paths.stats.required).toBe(true);

        expect(schemaArg.paths.fromTimestamp).toBeDefined();
        expect(schemaArg.paths.fromTimestamp.type).toBe(Date);
        expect(schemaArg.paths.fromTimestamp.required).toBe(true);
        expect(schemaArg.paths.fromTimestamp.unique).toBe(true);

        expect(schemaArg.paths.toTimestamp).toBeDefined();
        expect(schemaArg.paths.toTimestamp.type).toBe(Date);
        expect(schemaArg.paths.toTimestamp.required).toBe(true);
        expect(schemaArg.paths.toTimestamp.unique).toBe(true);

        expect(schemaArg.paths.type).toBeDefined();
        expect(schemaArg.paths.type.type).toBe(String);
        expect(schemaArg.paths.type.required).toBe(true);
        expect(schemaArg.paths.type.enum).toEqual(["weekly", "daily"]);
    });
});
