import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    if (definition && typeof definition === 'object') {
        Object.keys(definition).forEach(key => {
            this.paths[key] = definition[key];
        });
    }

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('ApiKey Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ApiKey')];

        const ApiKeyModule = await import('../../models/ApiKey');
        const ApiKey = ApiKeyModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('ApiKey', expect.any(Object), 'api_keys');
        expect(ApiKey).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths).toBeDefined();

        const apiKeyDefault = schemaArg.paths.api_key.default();
        expect(typeof apiKeyDefault).toBe('string');
        expect(apiKeyDefault.length).toBe(32);

        expect(schemaArg.paths.is_deleted.default).toBe(false);
        expect(schemaArg.paths.is_revoked.default).toBe(false);
        expect(schemaArg.paths.requests.default).toBe(0);
        expect(schemaArg.paths.requests_endpoints.default).toEqual({});
        expect(schemaArg.paths.allowed_vessels.default).toEqual([]);

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});