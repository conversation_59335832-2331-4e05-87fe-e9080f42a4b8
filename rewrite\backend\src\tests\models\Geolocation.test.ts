import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('Geolocation Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/Geolocation')];

        const GeolocationModule = await import('../../models/Geolocation');
        const Geolocation = GeolocationModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('Geolocation', expect.any(Object), 'geolocations');
        expect(Geolocation).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.location).toBeDefined();
        expect(schemaArg.paths.location.type).toBeDefined();
        expect(schemaArg.paths.location.type.type).toBe(String);
        expect(schemaArg.paths.location.type.enum).toEqual(["Point"]);
        expect(schemaArg.paths.location.type.default).toBe("Point");
        expect(schemaArg.paths.location.coordinates).toBeDefined();
        expect(schemaArg.paths.location.coordinates.type).toEqual([Number]);
        expect(schemaArg.paths.location.coordinates.required).toBe(true);

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);

        expect(schemaArg.index).toHaveBeenCalledWith({ location: "2dsphere" });
    });
});
