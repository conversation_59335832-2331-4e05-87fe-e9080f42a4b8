/**
 * User Service Mocks
 * 
 * Professional mocks for User service/query testing
 */

import { jest } from '@jest/globals';
import { createMockUser } from '../models/user.mock';

// ============================================================================
// USER QUERY SERVICE MOCK
// ============================================================================

export const createUserQueryMock = () => ({
    getUser: jest.fn().mockResolvedValue(createMockUser()),
    getUserByEmail: jest.fn().mockResolvedValue(null),
    getUserById: jest.fn().mockResolvedValue(createMockUser()),
    createUser: jest.fn().mockResolvedValue(createMockUser()),
    updateUser: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
    deleteUser: jest.fn().mockResolvedValue({ deletedCount: 1 }),
    getUsersByOrganization: jest.fn().mockResolvedValue([createMockUser()]),
    getUsersByRole: jest.fn().mockResolvedValue([createMockUser()]),
    searchUsers: jest.fn().mockResolvedValue([createMockUser()]),
    countUsers: jest.fn().mockResolvedValue(1),
    validateUserCredentials: jest.fn().mockResolvedValue(true),
    updateUserTokens: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
    deactivateUser: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
    activateUser: jest.fn().mockResolvedValue({ modifiedCount: 1 })
});

// ============================================================================
// USER SERVICE SCENARIOS
// ============================================================================

export const setupUserQueryScenarios = () => {
    const mockQuery = createUserQueryMock();
    
    return {
        mockQuery,
        
        // Scenario: User exists
        userExists: (userData: any = {}) => {
            const user = createMockUser(userData);
            mockQuery.getUser.mockResolvedValue(user);
            mockQuery.getUserByEmail.mockResolvedValue(user);
            mockQuery.getUserById.mockResolvedValue(user);
            return user;
        },
        
        // Scenario: User not found
        userNotFound: () => {
            mockQuery.getUser.mockResolvedValue(null);
            mockQuery.getUserByEmail.mockResolvedValue(null);
            mockQuery.getUserById.mockResolvedValue(null);
        },
        
        // Scenario: User is deleted
        userDeleted: () => {
            const deletedUser = createMockUser({ is_deleted: true });
            mockQuery.getUser.mockResolvedValue(deletedUser);
            return deletedUser;
        },
        
        // Scenario: User is inactive
        userInactive: () => {
            const inactiveUser = createMockUser({ is_active: false });
            mockQuery.getUser.mockResolvedValue(inactiveUser);
            return inactiveUser;
        },
        
        // Scenario: User has no JWT tokens
        userNoTokens: () => {
            const userWithoutTokens = createMockUser({ jwt_tokens: [] });
            mockQuery.getUser.mockResolvedValue(userWithoutTokens);
            return userWithoutTokens;
        },
        
        // Scenario: User has specific JWT tokens
        userWithTokens: (tokens: string[]) => {
            const userWithTokens = createMockUser({ jwt_tokens: tokens });
            mockQuery.getUser.mockResolvedValue(userWithTokens);
            return userWithTokens;
        },
        
        // Scenario: Database error
        databaseError: (errorMessage = 'Database connection failed') => {
            const error = new Error(errorMessage);
            mockQuery.getUser.mockRejectedValue(error);
            mockQuery.getUserByEmail.mockRejectedValue(error);
            mockQuery.getUserById.mockRejectedValue(error);
            return error;
        }
    };
};

// ============================================================================
// USER AUTHENTICATION SERVICE MOCK
// ============================================================================

export const createUserAuthMock = () => ({
    hashPassword: jest.fn().mockResolvedValue('hashedPassword'),
    comparePassword: jest.fn().mockResolvedValue(true),
    generateJWT: jest.fn().mockReturnValue('mock-jwt-token'),
    verifyJWT: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    generateRefreshToken: jest.fn().mockReturnValue('mock-refresh-token'),
    verifyRefreshToken: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    generatePasswordResetToken: jest.fn().mockReturnValue('mock-reset-token'),
    verifyPasswordResetToken: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    generateEmailVerificationToken: jest.fn().mockReturnValue('mock-verification-token'),
    verifyEmailVerificationToken: jest.fn().mockReturnValue({ user_id: 'test-user-id' })
});

// ============================================================================
// USER PERMISSION SERVICE MOCK
// ============================================================================

export const createUserPermissionMock = () => ({
    getUserPermissions: jest.fn().mockResolvedValue(['read:users', 'write:users']),
    hasPermission: jest.fn().mockReturnValue(true),
    hasAnyPermission: jest.fn().mockReturnValue(true),
    hasAllPermissions: jest.fn().mockReturnValue(true),
    getUserRole: jest.fn().mockResolvedValue({ role_id: 1, role_name: 'Admin' }),
    canAccessEndpoint: jest.fn().mockReturnValue(true),
    canAccessResource: jest.fn().mockReturnValue(true)
});

// ============================================================================
// USER NOTIFICATION SERVICE MOCK
// ============================================================================

export const createUserNotificationMock = () => ({
    sendWelcomeEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    sendPasswordResetEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    sendEmailVerificationEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    sendAccountDeactivationEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    sendAccountDeletionEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    createInAppNotification: jest.fn().mockResolvedValue({ _id: 'notification-id' }),
    markNotificationAsRead: jest.fn().mockResolvedValue({ modifiedCount: 1 })
});

// ============================================================================
// USER SERVICE TEST UTILITIES
// ============================================================================

export const setupUserServiceTest = () => {
    const mockQuery = createUserQueryMock();
    const mockAuth = createUserAuthMock();
    const mockPermission = createUserPermissionMock();
    const mockNotification = createUserNotificationMock();
    
    return {
        mockQuery,
        mockAuth,
        mockPermission,
        mockNotification
    };
};

export const expectUserQueried = (mockQuery: any, query: any, options?: any) => {
    if (options) {
        expect(mockQuery.getUser).toHaveBeenCalledWith(query, options);
    } else {
        expect(mockQuery.getUser).toHaveBeenCalledWith(query);
    }
};

export const expectUserAuthenticated = (mockAuth: any, credentials: any) => {
    expect(mockAuth.verifyJWT).toHaveBeenCalledWith(credentials.token);
    expect(mockAuth.comparePassword).toHaveBeenCalledWith(credentials.password, expect.any(String));
};

export const expectUserPermissionChecked = (mockPermission: any, userId: string, permission: string) => {
    expect(mockPermission.hasPermission).toHaveBeenCalledWith(userId, permission);
};

export const expectUserNotified = (mockNotification: any, userId: string, type: string) => {
    switch (type) {
        case 'welcome':
            expect(mockNotification.sendWelcomeEmail).toHaveBeenCalledWith(userId);
            break;
        case 'password-reset':
            expect(mockNotification.sendPasswordResetEmail).toHaveBeenCalledWith(userId);
            break;
        case 'email-verification':
            expect(mockNotification.sendEmailVerificationEmail).toHaveBeenCalledWith(userId);
            break;
        default:
            expect(mockNotification.createInAppNotification).toHaveBeenCalledWith(userId, expect.any(Object));
    }
};

// ============================================================================
// CLEANUP
// ============================================================================

export const cleanupUserServiceTest = () => {
    jest.clearAllMocks();
};
