import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

const mockNormalizeName = jest.fn((name: string) => name.toLowerCase());

const MockSchema = function(this: any, definition: any) {
    this.definition = definition;
    this.paths = {};
    this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

    Object.keys(definition).forEach(key => {
        this.paths[key] = definition[key];
    });

    this.index = jest.fn();
    this.pre = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._pres.has(event)) {
            this.s.hooks._pres.set(event, []);
        }
        this.s.hooks._pres.get(event).push({ fn });
    });
    this.post = jest.fn((event: string, fn: Function) => {
        if (!this.s.hooks._posts.has(event)) {
            this.s.hooks._posts.set(event, []);
        }
        this.s.hooks._posts.get(event).push({ fn });
    });

    return this;
};

(MockSchema as any).Types = {
    ObjectId: function() { return 'mock-object-id'; }
};

const mockMongoose = {
    Schema: MockSchema,
    Types: {
        ObjectId: function() { return 'mock-object-id'; }
    }
};

const mockDb = {
    qm: {
        model: jest.fn((name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.modelName = name;
            MockModel.schema = schema;
            return MockModel;
        })
    }
};

describe('RegionGroup Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        jest.doMock('../../utils/functions', () => ({ normalizeName: mockNormalizeName }));
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/RegionGroup')];

        const RegionGroupModule = await import('../../models/RegionGroup');
        const RegionGroup = RegionGroupModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('RegionGroup', expect.any(Object), 'regions_groups');
        expect(RegionGroup).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);
        expect(schemaArg.paths.name.unique).toBe(true);

        expect(schemaArg.paths.timezone).toBeDefined();
        expect(schemaArg.paths.timezone.type).toBe(String);
        expect(schemaArg.paths.timezone.required).toBe(true);

        expect(schemaArg.paths.created_by).toBeDefined();
        expect(schemaArg.paths.created_by.type).toBeDefined();
        expect(schemaArg.paths.created_by.required).toBe(true);

        expect(schemaArg.paths.creation_timestamp).toBeDefined();
        expect(schemaArg.paths.creation_timestamp.type).toBe(Date);
        expect(schemaArg.paths.creation_timestamp.required).toBe(true);
        expect(schemaArg.paths.creation_timestamp.default).toBeDefined();

        const timestamp = schemaArg.paths.creation_timestamp.default();
        expect(typeof timestamp).toBe('string');
        expect(timestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        expect(schemaArg.pre).toHaveBeenCalledWith("save", expect.any(Function));
        expect(schemaArg.index).toHaveBeenCalledWith({ name: 1 }, { unique: true, collation: { locale: "en", strength: 2 } });

        const preSaveHookCall = schemaArg.pre.mock.calls.find((call: any) => call[0] === 'save');
        if (preSaveHookCall) {
            const preSaveHookFn = preSaveHookCall[1];
            const mockDoc = { name: 'Test Region Group' };
            const mockNext = jest.fn();
            preSaveHookFn.call(mockDoc, mockNext);
            expect(mockNormalizeName).toHaveBeenCalledWith('Test Region Group');
            expect(mockNext).toHaveBeenCalled();
        }
    });
});
