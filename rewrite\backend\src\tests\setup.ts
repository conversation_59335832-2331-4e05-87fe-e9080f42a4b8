import { NextFunction, Request, Response } from "express";

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.MONGO_URI = 'mongodb://localhost:27017/test';
process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';
process.env.AWS_REGION = 'us-east-1';

// Mock external modules that cause memory issues
jest.mock('express-rate-limit', () => jest.fn(() => (_req: Request, _res: Response, next: NextFunction) => next()));

// Mock heavy AWS modules to reduce memory usage
jest.mock('@aws-sdk/client-kinesis-video', () => ({
    KinesisVideoClient: jest.fn(),
}));

jest.mock('@aws-sdk/client-kinesis-video-archived-media', () => ({
    KinesisVideoArchivedMediaClient: jest.fn(),
}));

jest.mock('@aws-sdk/client-cloudfront', () => ({
    CloudFrontClient: jest.fn(),
}));

// Mock AWS SDK v2
jest.mock('aws-sdk', () => ({
    S3: jest.fn(() => ({
        upload: jest.fn().mockReturnValue({
            promise: jest.fn().mockResolvedValue({ Location: 'test-location' })
        }),
        deleteObject: jest.fn().mockReturnValue({
            promise: jest.fn().mockResolvedValue({})
        }),
        getSignedUrl: jest.fn().mockReturnValue('test-signed-url')
    })),
    config: {
        update: jest.fn()
    }
}));

// Mock Google Maps
jest.mock('@googlemaps/google-maps-services-js', () => ({
    Client: jest.fn(() => ({
        geocode: jest.fn().mockResolvedValue({
            data: {
                results: [{
                    formatted_address: 'Test Address',
                    plus_code: { global_code: 'TEST123' }
                }]
            }
        })
    }))
}));

// Mock Mongoose to prevent actual database connections
const mockObjectId = jest.fn((id) => id || '507f1f77bcf86cd799439011');
const mockSchema: any = jest.fn(() => ({
    pre: jest.fn(),
    post: jest.fn(),
    index: jest.fn(),
    methods: {},
    statics: {},
}));

// Add Schema.Types to the mock
mockSchema.Types = {
    ObjectId: mockObjectId,
    String: String,
    Number: Number,
    Boolean: Boolean,
    Array: Array,
    Date: Date,
};

// Mock mongoose connection
const mockConnection = {
    on: jest.fn(),
    once: jest.fn(),
    close: jest.fn(),
    readyState: 1
};

jest.mock('mongoose', () => ({
    connect: jest.fn().mockResolvedValue(mockConnection),
    connection: mockConnection,
    createConnection: jest.fn().mockReturnValue(mockConnection),
    set: jest.fn(),
    Schema: mockSchema,
    model: jest.fn().mockImplementation((_name: string, schema: any) => {
        const MockModel = function(this: any, data: any) {
            Object.assign(this, data);
        };
        MockModel.prototype = {};
        MockModel.find = jest.fn();
        MockModel.findOne = jest.fn();
        MockModel.findById = jest.fn();
        MockModel.findOneAndDelete = jest.fn();
        MockModel.findOneAndUpdate = jest.fn();
        MockModel.create = jest.fn();
        MockModel.updateOne = jest.fn();
        MockModel.deleteOne = jest.fn();
        MockModel.aggregate = jest.fn();
        MockModel.schema = schema;
        return MockModel;
    }),
    Types: {
        ObjectId: mockObjectId,
    },
    default: {
        connect: jest.fn().mockResolvedValue(mockConnection),
        connection: mockConnection,
        createConnection: jest.fn().mockReturnValue(mockConnection),
        set: jest.fn(),
        Schema: mockSchema,
        model: jest.fn().mockImplementation((_name: string, schema: any) => {
            const MockModel = function(this: any, data: any) {
                Object.assign(this, data);
            };
            MockModel.prototype = {};
            MockModel.find = jest.fn();
            MockModel.findOne = jest.fn();
            MockModel.findById = jest.fn();
            MockModel.findOneAndDelete = jest.fn();
            MockModel.findOneAndUpdate = jest.fn();
            MockModel.create = jest.fn();
            MockModel.updateOne = jest.fn();
            MockModel.deleteOne = jest.fn();
            MockModel.aggregate = jest.fn();
            MockModel.schema = schema;
            return MockModel;
        }),
        Types: {
            ObjectId: mockObjectId,
        },
    }
}));

// Create a comprehensive database connection mock
const createMockConnection = () => ({
    model: jest.fn().mockImplementation((_name: string, schema: any) => {
        const MockModel = function(this: any, data: any) {
            Object.assign(this, data);
        };
        MockModel.prototype = {};
        MockModel.find = jest.fn();
        MockModel.findOne = jest.fn();
        MockModel.findById = jest.fn();
        MockModel.findOneAndDelete = jest.fn();
        MockModel.findOneAndUpdate = jest.fn();
        MockModel.create = jest.fn();
        MockModel.updateOne = jest.fn();
        MockModel.deleteOne = jest.fn();
        MockModel.aggregate = jest.fn();
        MockModel.schema = schema;
        return MockModel;
    }),
    collection: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    close: jest.fn(),
    readyState: 1
});

// Mock database module with all connections
jest.mock('../modules/db', () => {
    const mockConnections = {
        qm: createMockConnection(),
        qmai: createMockConnection(),
        qmShared: createMockConnection(),
        locations: createMockConnection(),
        locationsOptimized: createMockConnection(),
        locationsRaw: createMockConnection(),
        ais: createMockConnection(),
        audio: createMockConnection(),
        lookups: createMockConnection(),
    };

    return {
        ...mockConnections,
        default: mockConnections
    };
});

// Mock JWT
jest.mock('jsonwebtoken', () => ({
    sign: jest.fn().mockReturnValue('mockedToken'),
    verify: jest.fn().mockReturnValue({ user_id: 'test-user-id' }),
    decode: jest.fn().mockReturnValue({ user_id: 'test-user-id' })
}));

// Mock nodemailer
jest.mock('nodemailer', () => ({
    createTransport: jest.fn().mockReturnValue({
        sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' })
    })
}));

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
    hash: jest.fn().mockResolvedValue('hashedPassword'),
    compare: jest.fn().mockResolvedValue(true),
    genSalt: jest.fn().mockResolvedValue('salt')
}));

// Mock socket.io
jest.mock('socket.io', () => ({
    Server: jest.fn(() => ({
        on: jest.fn(),
        emit: jest.fn(),
        use: jest.fn(),
        listen: jest.fn(),
        close: jest.fn()
    }))
}));

// Global test setup
beforeEach(() => {
    // Mock console methods to reduce output noise
    jest.spyOn(console, 'log').mockImplementation(() => { });
    jest.spyOn(console, 'error').mockImplementation(() => { });
    jest.spyOn(console, 'info').mockImplementation(() => { });
    jest.spyOn(console, 'warn').mockImplementation(() => { });

    // Clear any timers
    jest.clearAllTimers();
});

// Global cleanup after each test
afterEach(() => {
    // Restore all mocks
    jest.restoreAllMocks();

    // Clear all timers
    jest.clearAllTimers();

    // Force garbage collection if available
    if (global.gc) {
        global.gc();
    }
});

// Global cleanup after all tests
afterAll(() => {
    // Final cleanup
    jest.restoreAllMocks();
    jest.clearAllTimers();
});