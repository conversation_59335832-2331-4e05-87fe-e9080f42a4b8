name: Staging CI

on:
  push:
    branches: [ "staging" ]

jobs:
  build:

    runs-on: quartermaster-testing-ec2
    permissions:
      contents: read
      actions: read
      statuses: write
    strategy:
      matrix:
        node-version: [18.x ]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

##############################################################################################
################################### Staging CI ###############################################
##############################################################################################

    - name:
        Deploy to staging.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      run: |
        echo "Deploying to staging.quartermaster.us"
        cd ~/staging.quartermaster.us/
        git restore .
        git pull

    - name:
        Deploy to staging.quartermaster.us - Install Backend Dependencies
      run: |
        cd ~/staging.quartermaster.us/
        npm install

    - name:
        Deploy to staging.quartermaster.us - Install Frontend Dependencies
      run: |
        cd ~/staging.quartermaster.us/frontend/
        npm install

    - name:
        Deploy to staging.quartermaster.us - Clean and Create frontend .env File
      run: |
        cd ~/staging.quartermaster.us/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to staging.quartermaster.us - Populate Frontend .env File with Secrets
      run: |
        cd ~/staging.quartermaster.us/frontend/
        echo "${{ secrets.STAGING_QUARTERMASTER_US_FRONTEND }}" > .env

    - name: 
        Deploy to staging.quartermaster.us - Set BUILD_ID
      id: buildid
      run: echo "BUILD_ID=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT

    - name:
        Deploy to staging.quartermaster.us - Build Frontend
      run: |
        cd ~/staging.quartermaster.us/frontend/
        BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build

    - name: 
        Deploy to staging.quartermaster.us - Syslink Switch
      run: |
        cd ~/staging.quartermaster.us/frontend/
        ln -sfn dist-${{ steps.buildid.outputs.BUILD_ID }} dist

    - name: 
        Deploy to staging.quartermaster.us - Cleanup Old Builds
      run: |
        cd ~/staging.quartermaster.us/frontend/
        ls -dt dist-* | tail -n +4 | xargs -r rm -rf

    - name:
        Deploy to staging.quartermaster.us - Clean and Create backend .env File
      run: |
        cd ~/staging.quartermaster.us/
        rm -rf .env
        touch .env

    - name:
        Deploy to staging.quartermaster.us - Populate Backend .env File with Secrets
      run: |
        cd ~/staging.quartermaster.us/
        echo "${{ secrets.STAGING_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to staging.quartermaster.us - Restart PM2 Process
      run: |
        pm2 restart 'Quartermaster Staging'
    
##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        echo "URL=https://staging.quartermaster.us" >> $GITHUB_ENV

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nStaging CI\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nStaging CI\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}