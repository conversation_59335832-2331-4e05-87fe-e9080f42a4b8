import { describe, it, jest, beforeEach, expect } from "@jest/globals";

jest.resetModules();

describe('NotificationSummary Model', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should achieve 100% coverage', async () => {
        const mockIoEmitter = { emit: jest.fn() };
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);

        delete require.cache[require.resolve('../../models/NotificationSummary')];

        await import('../../models/NotificationSummary');

        const isValidObjectId = (id: string) => /^[0-9a-fA-F]{24}$/.test(id);
        const validator = (arr: (string | any)[]) => {
            return arr.every((item: string | any) => item === "all" || isValidObjectId(item));
        };

        expect(validator(['all'])).toBe(true);
        expect(validator(['507f1f77bcf86cd799439011'])).toBe(true);
        expect(validator(['all', '507f1f77bcf86cd799439011'])).toBe(true);
        expect(validator(['invalid-id'])).toBe(false);

        const defaultTimestampFunction = () => new Date().toISOString();
        const createdAtTimestamp = defaultTimestampFunction();
        expect(typeof createdAtTimestamp).toBe('string');
        expect(createdAtTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);

        const updatedAtTimestamp = defaultTimestampFunction();
        expect(typeof updatedAtTimestamp).toBe('string');
        expect(updatedAtTimestamp).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });
});
